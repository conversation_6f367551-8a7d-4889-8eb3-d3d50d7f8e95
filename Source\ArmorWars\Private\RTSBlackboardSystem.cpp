#include "RTSBlackboardSystem.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSFormationManager.h"
#include "RTSTeamManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

// Static key definitions
const FString URTSBlackboardComponent::KEY_UNIT_HEALTH = TEXT("UnitHealth");
const FString URTSBlackboardComponent::KEY_UNIT_HEALTH_PERCENTAGE = TEXT("UnitHealthPercentage");
const FString URTSBlackboardComponent::KEY_UNIT_POSITION = TEXT("UnitPosition");
const FString URTSBlackboardComponent::KEY_UNIT_ROTATION = TEXT("UnitRotation");
const FString URTSBlackboardComponent::KEY_UNIT_VELOCITY = TEXT("UnitVelocity");
const FString URTSBlackboardComponent::KEY_UNIT_SPEED = TEXT("UnitSpeed");
const FString URTSBlackboardComponent::KEY_UNIT_MAX_SPEED = TEXT("UnitMaxSpeed");
const FString URTSBlackboardComponent::KEY_UNIT_ATTACK_RANGE = TEXT("UnitAttackRange");
const FString URTSBlackboardComponent::KEY_UNIT_TEAM_ID = TEXT("UnitTeamID");

const FString URTSBlackboardComponent::KEY_CURRENT_COMMAND = TEXT("CurrentCommand");
const FString URTSBlackboardComponent::KEY_COMMAND_TYPE = TEXT("CommandType");
const FString URTSBlackboardComponent::KEY_COMMAND_TARGET_LOCATION = TEXT("CommandTargetLocation");
const FString URTSBlackboardComponent::KEY_COMMAND_TARGET_ACTOR = TEXT("CommandTargetActor");
const FString URTSBlackboardComponent::KEY_COMMAND_PRIORITY = TEXT("CommandPriority");
const FString URTSBlackboardComponent::KEY_HAS_COMMANDS = TEXT("HasCommands");
const FString URTSBlackboardComponent::KEY_COMMAND_QUEUE_SIZE = TEXT("CommandQueueSize");

const FString URTSBlackboardComponent::KEY_IN_FORMATION = TEXT("InFormation");
const FString URTSBlackboardComponent::KEY_FORMATION_LEADER = TEXT("FormationLeader");
const FString URTSBlackboardComponent::KEY_FORMATION_OFFSET = TEXT("FormationOffset");
const FString URTSBlackboardComponent::KEY_FORMATION_INDEX = TEXT("FormationIndex");
const FString URTSBlackboardComponent::KEY_FORMATION_TYPE = TEXT("FormationType");

const FString URTSBlackboardComponent::KEY_CURRENT_TARGET = TEXT("CurrentTarget");
const FString URTSBlackboardComponent::KEY_NEAREST_ENEMY = TEXT("NearestEnemy");
const FString URTSBlackboardComponent::KEY_ENEMY_COUNT = TEXT("EnemyCount");
const FString URTSBlackboardComponent::KEY_UNDER_ATTACK = TEXT("UnderAttack");
const FString URTSBlackboardComponent::KEY_LAST_ATTACKER = TEXT("LastAttacker");
const FString URTSBlackboardComponent::KEY_TACTICAL_ADVANTAGE = TEXT("TacticalAdvantage");

const FString URTSBlackboardComponent::KEY_MOVE_TARGET = TEXT("MoveTarget");
const FString URTSBlackboardComponent::KEY_PATH_BLOCKED = TEXT("PathBlocked");
const FString URTSBlackboardComponent::KEY_ALTERNATIVE_PATH = TEXT("AlternativePath");
const FString URTSBlackboardComponent::KEY_COLLISION_AVOIDANCE = TEXT("CollisionAvoidance");

URTSBlackboardComponent::URTSBlackboardComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f;
    
    bAutoUpdateUnitData = true;
    bAutoUpdateCommandData = true;
    bAutoUpdateFormationData = true;
    bAutoUpdateCombatData = true;
    UpdateInterval = 0.1f;
    LastUpdateTime = 0.0f;
}

void URTSBlackboardComponent::BeginPlay()
{
    Super::BeginPlay();
    
    InitializePredefinedKeys();
    UpdateAllData();
}

void URTSBlackboardComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    if (CurrentTime - LastUpdateTime >= UpdateInterval)
    {
        UpdateAllData();
        LastUpdateTime = CurrentTime;
    }
}

void URTSBlackboardComponent::SetBool(const FString& Key, bool Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::Bool);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::Bool);
    Entry.BoolValue = Value;
    SetValue(Key, Entry);
}

void URTSBlackboardComponent::SetInt(const FString& Key, int32 Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::Int);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::Int);
    Entry.IntValue = Value;
    SetValue(Key, Entry);
}

void URTSBlackboardComponent::SetFloat(const FString& Key, float Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::Float);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::Float);
    Entry.FloatValue = Value;
    SetValue(Key, Entry);
}

void URTSBlackboardComponent::SetString(const FString& Key, const FString& Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::String);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::String);
    Entry.StringValue = Value;
    SetValue(Key, Entry);
}

void URTSBlackboardComponent::SetVector(const FString& Key, const FVector& Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::Vector);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::Vector);
    Entry.VectorValue = Value;
    SetValue(Key, Entry);
}

void URTSBlackboardComponent::SetRotator(const FString& Key, const FRotator& Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::Rotator);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::Rotator);
    Entry.RotatorValue = Value;
    SetValue(Key, Entry);
}

void URTSBlackboardComponent::SetObject(const FString& Key, UObject* Value)
{
    CreateKeyIfNotExists(Key, ERTSBlackboardKeyType::Object);
    FRTSBlackboardEntry Entry(Key, ERTSBlackboardKeyType::Object);
    Entry.ObjectValue = Value;
    SetValue(Key, Entry);
}

bool URTSBlackboardComponent::GetBool(const FString& Key, bool DefaultValue) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::Bool ? Entry->BoolValue : DefaultValue;
}

int32 URTSBlackboardComponent::GetInt(const FString& Key, int32 DefaultValue) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::Int ? Entry->IntValue : DefaultValue;
}

float URTSBlackboardComponent::GetFloat(const FString& Key, float DefaultValue) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::Float ? Entry->FloatValue : DefaultValue;
}

FString URTSBlackboardComponent::GetString(const FString& Key, const FString& DefaultValue) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::String ? Entry->StringValue : DefaultValue;
}

FVector URTSBlackboardComponent::GetVector(const FString& Key, const FVector& DefaultValue) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::Vector ? Entry->VectorValue : DefaultValue;
}

FRotator URTSBlackboardComponent::GetRotator(const FString& Key, const FRotator& DefaultValue) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::Rotator ? Entry->RotatorValue : DefaultValue;
}

UObject* URTSBlackboardComponent::GetObject(const FString& Key) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry && Entry->KeyType == ERTSBlackboardKeyType::Object ? Entry->ObjectValue.Get() : nullptr;
}

ARTSBaseActor* URTSBlackboardComponent::GetActor(const FString& Key) const
{
    return Cast<ARTSBaseActor>(GetObject(Key));
}

ARTSUnit* URTSBlackboardComponent::GetUnit(const FString& Key) const
{
    return Cast<ARTSUnit>(GetObject(Key));
}

bool URTSBlackboardComponent::HasKey(const FString& Key) const
{
    return BlackboardData.Contains(Key);
}

void URTSBlackboardComponent::RemoveKey(const FString& Key)
{
    BlackboardData.Remove(Key);
}

void URTSBlackboardComponent::ClearAllKeys()
{
    BlackboardData.Empty();
}

TArray<FString> URTSBlackboardComponent::GetAllKeys() const
{
    TArray<FString> Keys;
    BlackboardData.GetKeys(Keys);
    return Keys;
}

ERTSBlackboardKeyType URTSBlackboardComponent::GetKeyType(const FString& Key) const
{
    const FRTSBlackboardEntry* Entry = GetEntry(Key);
    return Entry ? Entry->KeyType : ERTSBlackboardKeyType::String;
}

void URTSBlackboardComponent::InitializePredefinedKeys()
{
    // Add predefined keys to the blackboard
    for (const FRTSBlackboardEntry& PredefinedKey : PredefinedKeys)
    {
        if (!HasKey(PredefinedKey.Key))
        {
            BlackboardData.Add(PredefinedKey.Key, PredefinedKey);
        }
    }
    
    // Add common keys if not already present
    AddPredefinedKey(KEY_UNIT_HEALTH, ERTSBlackboardKeyType::Float);
    AddPredefinedKey(KEY_UNIT_HEALTH_PERCENTAGE, ERTSBlackboardKeyType::Float);
    AddPredefinedKey(KEY_UNIT_POSITION, ERTSBlackboardKeyType::Vector);
    AddPredefinedKey(KEY_UNIT_ROTATION, ERTSBlackboardKeyType::Rotator);
    AddPredefinedKey(KEY_UNIT_VELOCITY, ERTSBlackboardKeyType::Vector);
    AddPredefinedKey(KEY_UNIT_SPEED, ERTSBlackboardKeyType::Float);
    AddPredefinedKey(KEY_UNIT_MAX_SPEED, ERTSBlackboardKeyType::Float);
    AddPredefinedKey(KEY_UNIT_ATTACK_RANGE, ERTSBlackboardKeyType::Float);
    AddPredefinedKey(KEY_UNIT_TEAM_ID, ERTSBlackboardKeyType::Int);
    
    AddPredefinedKey(KEY_HAS_COMMANDS, ERTSBlackboardKeyType::Bool);
    AddPredefinedKey(KEY_COMMAND_QUEUE_SIZE, ERTSBlackboardKeyType::Int);
    AddPredefinedKey(KEY_COMMAND_TYPE, ERTSBlackboardKeyType::String);
    AddPredefinedKey(KEY_COMMAND_TARGET_LOCATION, ERTSBlackboardKeyType::Vector);
    AddPredefinedKey(KEY_COMMAND_TARGET_ACTOR, ERTSBlackboardKeyType::Object);
    AddPredefinedKey(KEY_COMMAND_PRIORITY, ERTSBlackboardKeyType::String);
    
    AddPredefinedKey(KEY_IN_FORMATION, ERTSBlackboardKeyType::Bool);
    AddPredefinedKey(KEY_FORMATION_LEADER, ERTSBlackboardKeyType::Object);
    AddPredefinedKey(KEY_FORMATION_OFFSET, ERTSBlackboardKeyType::Vector);
    AddPredefinedKey(KEY_FORMATION_INDEX, ERTSBlackboardKeyType::Int);
    AddPredefinedKey(KEY_FORMATION_TYPE, ERTSBlackboardKeyType::String);
    
    AddPredefinedKey(KEY_CURRENT_TARGET, ERTSBlackboardKeyType::Object);
    AddPredefinedKey(KEY_NEAREST_ENEMY, ERTSBlackboardKeyType::Object);
    AddPredefinedKey(KEY_ENEMY_COUNT, ERTSBlackboardKeyType::Int);
    AddPredefinedKey(KEY_UNDER_ATTACK, ERTSBlackboardKeyType::Bool);
    AddPredefinedKey(KEY_LAST_ATTACKER, ERTSBlackboardKeyType::Object);
    AddPredefinedKey(KEY_TACTICAL_ADVANTAGE, ERTSBlackboardKeyType::Float);
    
    AddPredefinedKey(KEY_MOVE_TARGET, ERTSBlackboardKeyType::Vector);
    AddPredefinedKey(KEY_PATH_BLOCKED, ERTSBlackboardKeyType::Bool);
    AddPredefinedKey(KEY_ALTERNATIVE_PATH, ERTSBlackboardKeyType::Vector);
    AddPredefinedKey(KEY_COLLISION_AVOIDANCE, ERTSBlackboardKeyType::Vector);
}

void URTSBlackboardComponent::AddPredefinedKey(const FString& Key, ERTSBlackboardKeyType KeyType)
{
    if (!HasKey(Key))
    {
        FRTSBlackboardEntry Entry(Key, KeyType);
        BlackboardData.Add(Key, Entry);
    }
}

ARTSUnit* URTSBlackboardComponent::GetOwnerUnit() const
{
    return Cast<ARTSUnit>(GetOwner());
}

URTSCommandComponent* URTSBlackboardComponent::GetOwnerCommandComponent() const
{
    ARTSUnit* Unit = GetOwnerUnit();
    return Unit ? Unit->GetCommandComponent() : nullptr;
}

void URTSBlackboardComponent::SetValue(const FString& Key, const FRTSBlackboardEntry& Entry)
{
    BlackboardData.Add(Key, Entry);
}

FRTSBlackboardEntry* URTSBlackboardComponent::GetEntry(const FString& Key)
{
    return BlackboardData.Find(Key);
}

const FRTSBlackboardEntry* URTSBlackboardComponent::GetEntry(const FString& Key) const
{
    return BlackboardData.Find(Key);
}

void URTSBlackboardComponent::CreateKeyIfNotExists(const FString& Key, ERTSBlackboardKeyType KeyType)
{
    if (!HasKey(Key))
    {
        FRTSBlackboardEntry Entry(Key, KeyType);
        BlackboardData.Add(Key, Entry);
    }
}

void URTSBlackboardComponent::UpdateUnitData()
{
    if (!bAutoUpdateUnitData)
    {
        return;
    }

    UpdateBasicUnitInfo();
    UpdateUnitStats();
}

void URTSBlackboardComponent::UpdateCommandData()
{
    if (!bAutoUpdateCommandData)
    {
        return;
    }

    UpdateCurrentCommand();
    UpdateCommandQueue();
}

void URTSBlackboardComponent::UpdateFormationData()
{
    if (!bAutoUpdateFormationData)
    {
        return;
    }

    UpdateFormationInfo();
}

void URTSBlackboardComponent::UpdateCombatData()
{
    if (!bAutoUpdateCombatData)
    {
        return;
    }

    UpdateCombatTargets();
    UpdateEnvironmentalData();
}

void URTSBlackboardComponent::UpdateAllData()
{
    UpdateUnitData();
    UpdateCommandData();
    UpdateFormationData();
    UpdateCombatData();
}

void URTSBlackboardComponent::UpdateBasicUnitInfo()
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }

    // Update basic unit information
    SetFloat(KEY_UNIT_HEALTH, Unit->GetCurrentHealth());
    SetFloat(KEY_UNIT_HEALTH_PERCENTAGE, Unit->GetHealthPercentage());
    SetVector(KEY_UNIT_POSITION, Unit->GetActorLocation());
    SetRotator(KEY_UNIT_ROTATION, Unit->GetActorRotation());
    SetVector(KEY_UNIT_VELOCITY, Unit->GetVelocity());
    SetFloat(KEY_UNIT_SPEED, Unit->GetVelocity().Size());
    SetInt(KEY_UNIT_TEAM_ID, Unit->GetTeamID());
}

void URTSBlackboardComponent::UpdateUnitStats()
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }

    // Update unit capabilities
    SetFloat(KEY_UNIT_MAX_SPEED, Unit->GetMovementSpeed());
    SetFloat(KEY_UNIT_ATTACK_RANGE, Unit->GetMaxAttackRange());
}

void URTSBlackboardComponent::UpdateCurrentCommand()
{
    URTSCommandComponent* CommandComp = GetOwnerCommandComponent();
    if (!CommandComp)
    {
        SetBool(KEY_HAS_COMMANDS, false);
        return;
    }

    bool bHasCommands = CommandComp->HasCommands();
    SetBool(KEY_HAS_COMMANDS, bHasCommands);

    if (bHasCommands)
    {
        FRTSCommand CurrentCommand = CommandComp->GetCurrentCommand();
        SetString(KEY_COMMAND_TYPE, UEnum::GetValueAsString(CurrentCommand.CommandType));
        SetVector(KEY_COMMAND_TARGET_LOCATION, CurrentCommand.TargetLocation);
        SetObject(KEY_COMMAND_TARGET_ACTOR, CurrentCommand.TargetActor.Get());
        SetString(KEY_COMMAND_PRIORITY, UEnum::GetValueAsString(CurrentCommand.Priority));
    }
}

void URTSBlackboardComponent::UpdateCommandQueue()
{
    URTSCommandComponent* CommandComp = GetOwnerCommandComponent();
    if (!CommandComp)
    {
        SetInt(KEY_COMMAND_QUEUE_SIZE, 0);
        return;
    }

    SetInt(KEY_COMMAND_QUEUE_SIZE, CommandComp->GetCommandQueueSize());
}

void URTSBlackboardComponent::UpdateFormationInfo()
{
    URTSCommandComponent* CommandComp = GetOwnerCommandComponent();
    if (!CommandComp)
    {
        SetBool(KEY_IN_FORMATION, false);
        return;
    }

    bool bInFormation = CommandComp->IsInFormation();
    SetBool(KEY_IN_FORMATION, bInFormation);

    if (bInFormation)
    {
        SetObject(KEY_FORMATION_LEADER, CommandComp->GetFormationLeader());
        SetVector(KEY_FORMATION_OFFSET, CommandComp->GetFormationOffset());
    }
}

void URTSBlackboardComponent::UpdateCombatTargets()
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit || !Unit->GetWorld())
    {
        return;
    }

    URTSTeamManager* TeamManager = Unit->GetWorld()->GetSubsystem<URTSTeamManager>();
    if (!TeamManager)
    {
        return;
    }

    // Find nearest enemy
    float AttackRange = Unit->GetMaxAttackRange();
    ARTSBaseActor* NearestEnemy = TeamManager->FindNearestEnemy(Unit->GetTeamID(), Unit->GetActorLocation(), AttackRange);
    SetObject(KEY_NEAREST_ENEMY, NearestEnemy);

    // Count enemies in range
    TArray<ARTSBaseActor*> EnemiesInRange = TeamManager->FindEnemiesInRange(Unit->GetTeamID(), Unit->GetActorLocation(), AttackRange);
    SetInt(KEY_ENEMY_COUNT, EnemiesInRange.Num());
}

void URTSBlackboardComponent::UpdateEnvironmentalData()
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }

    // Update movement target if unit is moving
    FVector MoveTarget = Unit->GetActorLocation(); // Default to current location
    SetVector(KEY_MOVE_TARGET, MoveTarget);

    // Update path blocking status (simplified)
    SetBool(KEY_PATH_BLOCKED, false); // This would need more complex pathfinding logic
}
