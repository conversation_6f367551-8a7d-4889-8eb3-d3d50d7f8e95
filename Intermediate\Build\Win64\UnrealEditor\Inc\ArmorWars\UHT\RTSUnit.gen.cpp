// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSUnit.h"
#include "RTSCommand.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSUnit() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSArmorComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSBlackboardComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalAIComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSUnitAIComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSAIBehavior();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandPriority();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationRole();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSMovementState();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSTechLevel();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSUnitDomain();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommand();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCapsuleComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSMovementState *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSMovementState;
static UEnum* ERTSMovementState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSMovementState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSMovementState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSMovementState, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSMovementState"));
	}
	return Z_Registration_Info_UEnum_ERTSMovementState.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSMovementState>()
{
	return ERTSMovementState_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement state enum for better movement control\n// NOTE: This movement state system is WORKING and ACTIVELY USED - DO NOT REMOVE\n// It provides proper RTS-style movement with turn-in-place and gradual turning behavior\n// Used by both movement components and fallback movement system\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
		{ "Moving.DisplayName", "Moving" },
		{ "Moving.Name", "ERTSMovementState::Moving" },
		{ "Stationary.DisplayName", "Stationary" },
		{ "Stationary.Name", "ERTSMovementState::Stationary" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement state enum for better movement control\nNOTE: This movement state system is WORKING and ACTIVELY USED - DO NOT REMOVE\nIt provides proper RTS-style movement with turn-in-place and gradual turning behavior\nUsed by both movement components and fallback movement system" },
#endif
		{ "TurningInPlace.DisplayName", "Turning In Place" },
		{ "TurningInPlace.Name", "ERTSMovementState::TurningInPlace" },
		{ "TurningWhileMoving.DisplayName", "Turning While Moving" },
		{ "TurningWhileMoving.Name", "ERTSMovementState::TurningWhileMoving" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSMovementState::Stationary", (int64)ERTSMovementState::Stationary },
		{ "ERTSMovementState::TurningInPlace", (int64)ERTSMovementState::TurningInPlace },
		{ "ERTSMovementState::Moving", (int64)ERTSMovementState::Moving },
		{ "ERTSMovementState::TurningWhileMoving", (int64)ERTSMovementState::TurningWhileMoving },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSMovementState",
	"ERTSMovementState",
	Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSMovementState()
{
	if (!Z_Registration_Info_UEnum_ERTSMovementState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSMovementState.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSMovementState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSMovementState.InnerSingleton;
}
// ********** End Enum ERTSMovementState ***********************************************************

// ********** Begin Delegate FOnHealthChanged ******************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnHealthChanged_Parms
	{
		float CurrentHealth;
		float MaxHealth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate declarations (from RTSBaseActor)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate declarations (from RTSBaseActor)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnHealthChanged_Parms, CurrentHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnHealthChanged_Parms, MaxHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::NewProp_MaxHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnHealthChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnHealthChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnHealthChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float CurrentHealth, float MaxHealth)
{
	struct _Script_ArmorWars_eventOnHealthChanged_Parms
	{
		float CurrentHealth;
		float MaxHealth;
	};
	_Script_ArmorWars_eventOnHealthChanged_Parms Parms;
	Parms.CurrentHealth=CurrentHealth;
	Parms.MaxHealth=MaxHealth;
	OnHealthChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHealthChanged ********************************************************

// ********** Begin Delegate FOnDamageReceived *****************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnDamageReceived_Parms
	{
		float DamageAmount;
		AActor* DamageSource;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnDamageReceived_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnDamageReceived_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnDamageReceived__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::_Script_ArmorWars_eventOnDamageReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::_Script_ArmorWars_eventOnDamageReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnDamageReceived_DelegateWrapper(const FMulticastScriptDelegate& OnDamageReceived, float DamageAmount, AActor* DamageSource)
{
	struct _Script_ArmorWars_eventOnDamageReceived_Parms
	{
		float DamageAmount;
		AActor* DamageSource;
	};
	_Script_ArmorWars_eventOnDamageReceived_Parms Parms;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageSource=DamageSource;
	OnDamageReceived.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDamageReceived *******************************************************

// ********** Begin Delegate FOnActorDeath *********************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnActorDeath_Parms
	{
		AActor* DeadActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeadActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::NewProp_DeadActor = { "DeadActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnActorDeath_Parms, DeadActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::NewProp_DeadActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnActorDeath__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::_Script_ArmorWars_eventOnActorDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::_Script_ArmorWars_eventOnActorDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnActorDeath_DelegateWrapper(const FMulticastScriptDelegate& OnActorDeath, AActor* DeadActor)
{
	struct _Script_ArmorWars_eventOnActorDeath_Parms
	{
		AActor* DeadActor;
	};
	_Script_ArmorWars_eventOnActorDeath_Parms Parms;
	Parms.DeadActor=DeadActor;
	OnActorDeath.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorDeath ***********************************************************

// ********** Begin Delegate FOnUnitSelectionChanged ***********************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnUnitSelectionChanged_Parms
	{
		AActor* Actor;
		bool bSelected;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_bSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSelected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnUnitSelectionChanged_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::NewProp_bSelected_SetBit(void* Obj)
{
	((_Script_ArmorWars_eventOnUnitSelectionChanged_Parms*)Obj)->bSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::NewProp_bSelected = { "bSelected", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_ArmorWars_eventOnUnitSelectionChanged_Parms), &Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::NewProp_bSelected_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::NewProp_bSelected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnUnitSelectionChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnUnitSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnUnitSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnUnitSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnUnitSelectionChanged, AActor* Actor, bool bSelected)
{
	struct _Script_ArmorWars_eventOnUnitSelectionChanged_Parms
	{
		AActor* Actor;
		bool bSelected;
	};
	_Script_ArmorWars_eventOnUnitSelectionChanged_Parms Parms;
	Parms.Actor=Actor;
	Parms.bSelected=bSelected ? true : false;
	OnUnitSelectionChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnUnitSelectionChanged *************************************************

// ********** Begin Delegate FOnMovementChanged ****************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnMovementChanged_Parms
	{
		ARTSUnit* Unit;
		FVector TargetLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnMovementChanged_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnMovementChanged_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::NewProp_TargetLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnMovementChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnMovementChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnMovementChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMovementChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMovementChanged, ARTSUnit* Unit, FVector TargetLocation)
{
	struct _Script_ArmorWars_eventOnMovementChanged_Parms
	{
		ARTSUnit* Unit;
		FVector TargetLocation;
	};
	_Script_ArmorWars_eventOnMovementChanged_Parms Parms;
	Parms.Unit=Unit;
	Parms.TargetLocation=TargetLocation;
	OnMovementChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMovementChanged ******************************************************

// ********** Begin Class ARTSUnit Function AttackMoveToLocation ***********************************
struct Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics
{
	struct RTSUnit_eventAttackMoveToLocation_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventAttackMoveToLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "AttackMoveToLocation", Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::RTSUnit_eventAttackMoveToLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::RTSUnit_eventAttackMoveToLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execAttackMoveToLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AttackMoveToLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function AttackMoveToLocation *************************************

// ********** Begin Class ARTSUnit Function AttackTarget *******************************************
struct Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics
{
	struct RTSUnit_eventAttackTarget_Parms
	{
		AActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat functions using weapon controller\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat functions using weapon controller" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventAttackTarget_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "AttackTarget", Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::RTSUnit_eventAttackTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::RTSUnit_eventAttackTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_AttackTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_AttackTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execAttackTarget)
{
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AttackTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function AttackTarget *********************************************

// ********** Begin Class ARTSUnit Function CalculateCollisionAvoidance ****************************
struct Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics
{
	struct RTSUnit_eventCalculateCollisionAvoidance_Parms
	{
		FVector DesiredDirection;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision avoidance functions\n// NOTE: These collision avoidance functions are WORKING and ACTIVELY USED - DO NOT REMOVE\n// They prevent units from moving through each other and provide smooth unit separation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision avoidance functions\nNOTE: These collision avoidance functions are WORKING and ACTIVELY USED - DO NOT REMOVE\nThey prevent units from moving through each other and provide smooth unit separation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesiredDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesiredDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::NewProp_DesiredDirection = { "DesiredDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventCalculateCollisionAvoidance_Parms, DesiredDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesiredDirection_MetaData), NewProp_DesiredDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventCalculateCollisionAvoidance_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::NewProp_DesiredDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "CalculateCollisionAvoidance", Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::RTSUnit_eventCalculateCollisionAvoidance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::RTSUnit_eventCalculateCollisionAvoidance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execCalculateCollisionAvoidance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DesiredDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateCollisionAvoidance(Z_Param_Out_DesiredDirection);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function CalculateCollisionAvoidance ******************************

// ********** Begin Class ARTSUnit Function CanAttackTarget ****************************************
struct Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics
{
	struct RTSUnit_eventCanAttackTarget_Parms
	{
		const AActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventCanAttackTarget_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
void Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventCanAttackTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventCanAttackTarget_Parms), &Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "CanAttackTarget", Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::RTSUnit_eventCanAttackTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::RTSUnit_eventCanAttackTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_CanAttackTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_CanAttackTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execCanAttackTarget)
{
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAttackTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function CanAttackTarget ******************************************

// ********** Begin Class ARTSUnit Function CanBeProducedByTechLevel *******************************
struct Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics
{
	struct RTSUnit_eventCanBeProducedByTechLevel_Parms
	{
		ERTSTechLevel FactoryTechLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tech" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FactoryTechLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FactoryTechLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_FactoryTechLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_FactoryTechLevel = { "FactoryTechLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventCanBeProducedByTechLevel_Parms, FactoryTechLevel), Z_Construct_UEnum_ArmorWars_ERTSTechLevel, METADATA_PARAMS(0, nullptr) }; // 544529483
void Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventCanBeProducedByTechLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventCanBeProducedByTechLevel_Parms), &Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_FactoryTechLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_FactoryTechLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "CanBeProducedByTechLevel", Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::RTSUnit_eventCanBeProducedByTechLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::RTSUnit_eventCanBeProducedByTechLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execCanBeProducedByTechLevel)
{
	P_GET_ENUM(ERTSTechLevel,Z_Param_FactoryTechLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanBeProducedByTechLevel(ERTSTechLevel(Z_Param_FactoryTechLevel));
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function CanBeProducedByTechLevel *********************************

// ********** Begin Class ARTSUnit Function CanTargetDomain ****************************************
struct Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics
{
	struct RTSUnit_eventCanTargetDomain_Parms
	{
		ERTSUnitDomain Domain;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Domain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Domain;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_Domain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_Domain = { "Domain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventCanTargetDomain_Parms, Domain), Z_Construct_UEnum_ArmorWars_ERTSUnitDomain, METADATA_PARAMS(0, nullptr) }; // 3001890894
void Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventCanTargetDomain_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventCanTargetDomain_Parms), &Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_Domain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_Domain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "CanTargetDomain", Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::RTSUnit_eventCanTargetDomain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::RTSUnit_eventCanTargetDomain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_CanTargetDomain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_CanTargetDomain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execCanTargetDomain)
{
	P_GET_ENUM(ERTSUnitDomain,Z_Param_Domain);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanTargetDomain(ERTSUnitDomain(Z_Param_Domain));
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function CanTargetDomain ******************************************

// ********** Begin Class ARTSUnit Function ClearCommandQueue **************************************
struct Z_Construct_UFunction_ARTSUnit_ClearCommandQueue_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_ClearCommandQueue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "ClearCommandQueue", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_ClearCommandQueue_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_ClearCommandQueue_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_ClearCommandQueue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_ClearCommandQueue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execClearCommandQueue)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCommandQueue();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function ClearCommandQueue ****************************************

// ********** Begin Class ARTSUnit Function FindEnemiesInRange *************************************
struct Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics
{
	struct RTSUnit_eventFindEnemiesInRange_Parms
	{
		float Range;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventFindEnemiesInRange_Parms, Range), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventFindEnemiesInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "FindEnemiesInRange", Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::RTSUnit_eventFindEnemiesInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::RTSUnit_eventFindEnemiesInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execFindEnemiesInRange)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Range);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FindEnemiesInRange(Z_Param_Range);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function FindEnemiesInRange ***************************************

// ********** Begin Class ARTSUnit Function GetAttackDamage ****************************************
struct Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics
{
	struct RTSUnit_eventGetAttackDamage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetAttackDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetAttackDamage", Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::RTSUnit_eventGetAttackDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::RTSUnit_eventGetAttackDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetAttackDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetAttackDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetAttackDamage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAttackDamage();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetAttackDamage ******************************************

// ********** Begin Class ARTSUnit Function GetAttackRange *****************************************
struct Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics
{
	struct RTSUnit_eventGetAttackRange_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetAttackRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetAttackRange", Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::RTSUnit_eventGetAttackRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::RTSUnit_eventGetAttackRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetAttackRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetAttackRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetAttackRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAttackRange();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetAttackRange *******************************************

// ********** Begin Class ARTSUnit Function GetBlackboardComponent *********************************
struct Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics
{
	struct RTSUnit_eventGetBlackboardComponent_Parms
	{
		URTSBlackboardComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|AI" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetBlackboardComponent_Parms, ReturnValue), Z_Construct_UClass_URTSBlackboardComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetBlackboardComponent", Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::RTSUnit_eventGetBlackboardComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::RTSUnit_eventGetBlackboardComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetBlackboardComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBlackboardComponent**)Z_Param__Result=P_THIS->GetBlackboardComponent();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetBlackboardComponent ***********************************

// ********** Begin Class ARTSUnit Function GetCommandComponent ************************************
struct Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics
{
	struct RTSUnit_eventGetCommandComponent_Parms
	{
		URTSCommandComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetCommandComponent_Parms, ReturnValue), Z_Construct_UClass_URTSCommandComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetCommandComponent", Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::RTSUnit_eventGetCommandComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::RTSUnit_eventGetCommandComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetCommandComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetCommandComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetCommandComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSCommandComponent**)Z_Param__Result=P_THIS->GetCommandComponent();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetCommandComponent **************************************

// ********** Begin Class ARTSUnit Function GetCurrentCommand **************************************
struct Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics
{
	struct RTSUnit_eventGetCurrentCommand_Parms
	{
		FRTSCommand ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetCurrentCommand_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(0, nullptr) }; // 1544416680
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetCurrentCommand", Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::RTSUnit_eventGetCurrentCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::RTSUnit_eventGetCurrentCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetCurrentCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSCommand*)Z_Param__Result=P_THIS->GetCurrentCommand();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetCurrentCommand ****************************************

// ********** Begin Class ARTSUnit Function GetCurrentTarget ***************************************
struct Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics
{
	struct RTSUnit_eventGetCurrentTarget_Parms
	{
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetCurrentTarget_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetCurrentTarget", Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::RTSUnit_eventGetCurrentTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::RTSUnit_eventGetCurrentTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetCurrentTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetCurrentTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetCurrentTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->GetCurrentTarget();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetCurrentTarget *****************************************

// ********** Begin Class ARTSUnit Function GetDistanceToTarget ************************************
struct Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics
{
	struct RTSUnit_eventGetDistanceToTarget_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetDistanceToTarget_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetDistanceToTarget", Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::RTSUnit_eventGetDistanceToTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::RTSUnit_eventGetDistanceToTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetDistanceToTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDistanceToTarget();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetDistanceToTarget **************************************

// ********** Begin Class ARTSUnit Function GetFormationOffset *************************************
struct Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics
{
	struct RTSUnit_eventGetFormationOffset_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetFormationOffset_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetFormationOffset", Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::RTSUnit_eventGetFormationOffset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::RTSUnit_eventGetFormationOffset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetFormationOffset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetFormationOffset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetFormationOffset)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetFormationOffset();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetFormationOffset ***************************************

// ********** Begin Class ARTSUnit Function GetMaxAttackRange **************************************
struct Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics
{
	struct RTSUnit_eventGetMaxAttackRange_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetMaxAttackRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetMaxAttackRange", Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::RTSUnit_eventGetMaxAttackRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::RTSUnit_eventGetMaxAttackRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetMaxAttackRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaxAttackRange();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetMaxAttackRange ****************************************

// ********** Begin Class ARTSUnit Function GetMovementSpeed ***************************************
struct Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics
{
	struct RTSUnit_eventGetMovementSpeed_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetMovementSpeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetMovementSpeed", Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::RTSUnit_eventGetMovementSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::RTSUnit_eventGetMovementSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetMovementSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetMovementSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetMovementSpeed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMovementSpeed();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetMovementSpeed *****************************************

// ********** Begin Class ARTSUnit Function GetMovementState ***************************************
struct Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics
{
	struct RTSUnit_eventGetMovementState_Parms
	{
		ERTSMovementState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement state functions\n// NOTE: These movement state functions are WORKING and ACTIVELY USED - DO NOT REMOVE\n// They provide proper RTS movement behavior and state tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement state functions\nNOTE: These movement state functions are WORKING and ACTIVELY USED - DO NOT REMOVE\nThey provide proper RTS movement behavior and state tracking" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetMovementState_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSMovementState, METADATA_PARAMS(0, nullptr) }; // 1335892957
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetMovementState", Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::RTSUnit_eventGetMovementState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::RTSUnit_eventGetMovementState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetMovementState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetMovementState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetMovementState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSMovementState*)Z_Param__Result=P_THIS->GetMovementState();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetMovementState *****************************************

// ********** Begin Class ARTSUnit Function GetNearbyUnits *****************************************
struct Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics
{
	struct RTSUnit_eventGetNearbyUnits_Parms
	{
		float Radius;
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetNearbyUnits_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetNearbyUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetNearbyUnits", Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::RTSUnit_eventGetNearbyUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::RTSUnit_eventGetNearbyUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetNearbyUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetNearbyUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetNearbyUnits)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetNearbyUnits(Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetNearbyUnits *******************************************

// ********** Begin Class ARTSUnit Function GetTotalDamagePerSecond ********************************
struct Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics
{
	struct RTSUnit_eventGetTotalDamagePerSecond_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetTotalDamagePerSecond_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetTotalDamagePerSecond", Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::RTSUnit_eventGetTotalDamagePerSecond_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::RTSUnit_eventGetTotalDamagePerSecond_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetTotalDamagePerSecond)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalDamagePerSecond();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetTotalDamagePerSecond **********************************

// ********** Begin Class ARTSUnit Function GetUnitDomain ******************************************
struct Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics
{
	struct RTSUnit_eventGetUnitDomain_Parms
	{
		ERTSUnitDomain ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Unit" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetUnitDomain_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSUnitDomain, METADATA_PARAMS(0, nullptr) }; // 3001890894
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetUnitDomain", Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::RTSUnit_eventGetUnitDomain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::RTSUnit_eventGetUnitDomain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetUnitDomain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetUnitDomain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetUnitDomain)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSUnitDomain*)Z_Param__Result=P_THIS->GetUnitDomain();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetUnitDomain ********************************************

// ********** Begin Class ARTSUnit Function GetWeaponController ************************************
struct Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics
{
	struct RTSUnit_eventGetWeaponController_Parms
	{
		URTSWeaponController* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Weapons" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventGetWeaponController_Parms, ReturnValue), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "GetWeaponController", Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::RTSUnit_eventGetWeaponController_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::RTSUnit_eventGetWeaponController_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_GetWeaponController()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_GetWeaponController_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execGetWeaponController)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSWeaponController**)Z_Param__Result=P_THIS->GetWeaponController();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function GetWeaponController **************************************

// ********** Begin Class ARTSUnit Function HasCommands ********************************************
struct Z_Construct_UFunction_ARTSUnit_HasCommands_Statics
{
	struct RTSUnit_eventHasCommands_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventHasCommands_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventHasCommands_Parms), &Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "HasCommands", Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::RTSUnit_eventHasCommands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::RTSUnit_eventHasCommands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_HasCommands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_HasCommands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execHasCommands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasCommands();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function HasCommands **********************************************

// ********** Begin Class ARTSUnit Function HasWeapons *********************************************
struct Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics
{
	struct RTSUnit_eventHasWeapons_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventHasWeapons_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventHasWeapons_Parms), &Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "HasWeapons", Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::RTSUnit_eventHasWeapons_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::RTSUnit_eventHasWeapons_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_HasWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_HasWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execHasWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasWeapons();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function HasWeapons ***********************************************

// ********** Begin Class ARTSUnit Function IsAdvancedUnit *****************************************
struct Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics
{
	struct RTSUnit_eventIsAdvancedUnit_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tech" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Additional tech level functions specific to units\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional tech level functions specific to units" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsAdvancedUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsAdvancedUnit_Parms), &Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsAdvancedUnit", Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::RTSUnit_eventIsAdvancedUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::RTSUnit_eventIsAdvancedUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsAdvancedUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAdvancedUnit();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsAdvancedUnit *******************************************

// ********** Begin Class ARTSUnit Function IsAirUnit **********************************************
struct Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics
{
	struct RTSUnit_eventIsAirUnit_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Unit" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsAirUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsAirUnit_Parms), &Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsAirUnit", Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::RTSUnit_eventIsAirUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::RTSUnit_eventIsAirUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsAirUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsAirUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsAirUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAirUnit();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsAirUnit ************************************************

// ********** Begin Class ARTSUnit Function IsAttacking ********************************************
struct Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics
{
	struct RTSUnit_eventIsAttacking_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsAttacking_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsAttacking_Parms), &Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsAttacking", Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::RTSUnit_eventIsAttacking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::RTSUnit_eventIsAttacking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsAttacking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsAttacking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsAttacking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAttacking();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsAttacking **********************************************

// ********** Begin Class ARTSUnit Function IsInAttackRange ****************************************
struct Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics
{
	struct RTSUnit_eventIsInAttackRange_Parms
	{
		const AActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIsInAttackRange_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
void Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsInAttackRange_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsInAttackRange_Parms), &Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsInAttackRange", Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::RTSUnit_eventIsInAttackRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::RTSUnit_eventIsInAttackRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsInAttackRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsInAttackRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsInAttackRange)
{
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInAttackRange(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsInAttackRange ******************************************

// ********** Begin Class ARTSUnit Function IsInFormation ******************************************
struct Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics
{
	struct RTSUnit_eventIsInFormation_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsInFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsInFormation_Parms), &Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsInFormation", Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::RTSUnit_eventIsInFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::RTSUnit_eventIsInFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsInFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsInFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsInFormation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInFormation();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsInFormation ********************************************

// ********** Begin Class ARTSUnit Function IsLandUnit *********************************************
struct Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics
{
	struct RTSUnit_eventIsLandUnit_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Domain-specific functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Domain-specific functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsLandUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsLandUnit_Parms), &Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsLandUnit", Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::RTSUnit_eventIsLandUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::RTSUnit_eventIsLandUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsLandUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsLandUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsLandUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLandUnit();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsLandUnit ***********************************************

// ********** Begin Class ARTSUnit Function IsMoving ***********************************************
struct Z_Construct_UFunction_ARTSUnit_IsMoving_Statics
{
	struct RTSUnit_eventIsMoving_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsMoving_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsMoving_Parms), &Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsMoving", Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::RTSUnit_eventIsMoving_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::RTSUnit_eventIsMoving_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsMoving()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsMoving_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsMoving)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMoving();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsMoving *************************************************

// ********** Begin Class ARTSUnit Function IsSeaUnit **********************************************
struct Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics
{
	struct RTSUnit_eventIsSeaUnit_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Unit" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsSeaUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsSeaUnit_Parms), &Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsSeaUnit", Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::RTSUnit_eventIsSeaUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::RTSUnit_eventIsSeaUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsSeaUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsSeaUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsSeaUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSeaUnit();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsSeaUnit ************************************************

// ********** Begin Class ARTSUnit Function IsSubnauticalUnit **************************************
struct Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics
{
	struct RTSUnit_eventIsSubnauticalUnit_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Unit" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIsSubnauticalUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIsSubnauticalUnit_Parms), &Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IsSubnauticalUnit", Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::RTSUnit_eventIsSubnauticalUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::RTSUnit_eventIsSubnauticalUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIsSubnauticalUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSubnauticalUnit();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IsSubnauticalUnit ****************************************

// ********** Begin Class ARTSUnit Function IssueAttackCommand *************************************
struct Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics
{
	struct RTSUnit_eventIssueAttackCommand_Parms
	{
		ARTSBaseActor* Target;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIssueAttackCommand_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIssueAttackCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueAttackCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueAttackCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueAttackCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueAttackCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IssueAttackCommand", Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::RTSUnit_eventIssueAttackCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::RTSUnit_eventIssueAttackCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IssueAttackCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IssueAttackCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIssueAttackCommand)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueAttackCommand(Z_Param_Target,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IssueAttackCommand ***************************************

// ********** Begin Class ARTSUnit Function IssueCommand *******************************************
struct Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics
{
	struct RTSUnit_eventIssueCommand_Parms
	{
		FRTSCommand Command;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command System Functions\n" },
#endif
		{ "CPP_Default_bClearQueue", "false" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command System Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIssueCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
void Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IssueCommand", Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::RTSUnit_eventIssueCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::RTSUnit_eventIssueCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IssueCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IssueCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIssueCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueCommand(Z_Param_Out_Command,Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IssueCommand *********************************************

// ********** Begin Class ARTSUnit Function IssueMoveCommand ***************************************
struct Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics
{
	struct RTSUnit_eventIssueMoveCommand_Parms
	{
		FVector MoveTargetLocation;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoveTargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MoveTargetLocation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_MoveTargetLocation = { "MoveTargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIssueMoveCommand_Parms, MoveTargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoveTargetLocation_MetaData), NewProp_MoveTargetLocation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIssueMoveCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueMoveCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueMoveCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueMoveCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueMoveCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_MoveTargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IssueMoveCommand", Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::RTSUnit_eventIssueMoveCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::RTSUnit_eventIssueMoveCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IssueMoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IssueMoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIssueMoveCommand)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_MoveTargetLocation);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueMoveCommand(Z_Param_Out_MoveTargetLocation,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IssueMoveCommand *****************************************

// ********** Begin Class ARTSUnit Function IssueStopCommand ***************************************
struct Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics
{
	struct RTSUnit_eventIssueStopCommand_Parms
	{
		ERTSCommandPriority Priority;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Commands" },
		{ "CPP_Default_Priority", "High" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventIssueStopCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSUnit_eventIssueStopCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventIssueStopCommand_Parms), &Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "IssueStopCommand", Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::RTSUnit_eventIssueStopCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::RTSUnit_eventIssueStopCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_IssueStopCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_IssueStopCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execIssueStopCommand)
{
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueStopCommand(ERTSCommandPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function IssueStopCommand *****************************************

// ********** Begin Class ARTSUnit Function JoinFormation ******************************************
struct Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics
{
	struct RTSUnit_eventJoinFormation_Parms
	{
		ARTSUnit* Leader;
		ERTSFormationRole FormationRole;
		FVector Offset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|AI" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Leader;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationRole_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationRole;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_Leader = { "Leader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventJoinFormation_Parms, Leader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_FormationRole_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_FormationRole = { "FormationRole", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventJoinFormation_Parms, FormationRole), Z_Construct_UEnum_ArmorWars_ERTSFormationRole, METADATA_PARAMS(0, nullptr) }; // 439520218
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventJoinFormation_Parms, Offset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_Leader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_FormationRole_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_FormationRole,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::NewProp_Offset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "JoinFormation", Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::RTSUnit_eventJoinFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::RTSUnit_eventJoinFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_JoinFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_JoinFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execJoinFormation)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Leader);
	P_GET_ENUM(ERTSFormationRole,Z_Param_FormationRole);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Offset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->JoinFormation(Z_Param_Leader,ERTSFormationRole(Z_Param_FormationRole),Z_Param_Out_Offset);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function JoinFormation ********************************************

// ********** Begin Class ARTSUnit Function LeaveFormation *****************************************
struct Z_Construct_UFunction_ARTSUnit_LeaveFormation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|AI" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_LeaveFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "LeaveFormation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_LeaveFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_LeaveFormation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_LeaveFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_LeaveFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execLeaveFormation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LeaveFormation();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function LeaveFormation *******************************************

// ********** Begin Class ARTSUnit Function MoveToLocation *****************************************
struct Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics
{
	struct RTSUnit_eventMoveToLocation_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventMoveToLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "MoveToLocation", Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::RTSUnit_eventMoveToLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::RTSUnit_eventMoveToLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_MoveToLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_MoveToLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execMoveToLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveToLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function MoveToLocation *******************************************

// ********** Begin Class ARTSUnit Function MoveToLocationSynchronized *****************************
struct Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics
{
	struct RTSUnit_eventMoveToLocationSynchronized_Parms
	{
		FVector Location;
		float SynchronizedSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SynchronizedSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventMoveToLocationSynchronized_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::NewProp_SynchronizedSpeed = { "SynchronizedSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventMoveToLocationSynchronized_Parms, SynchronizedSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::NewProp_SynchronizedSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "MoveToLocationSynchronized", Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::RTSUnit_eventMoveToLocationSynchronized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::RTSUnit_eventMoveToLocationSynchronized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execMoveToLocationSynchronized)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SynchronizedSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveToLocationSynchronized(Z_Param_Out_Location,Z_Param_SynchronizedSpeed);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function MoveToLocationSynchronized *******************************

// ********** Begin Class ARTSUnit Function MoveToLocationWithFormation ****************************
struct Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics
{
	struct RTSUnit_eventMoveToLocationWithFormation_Parms
	{
		FVector Location;
		FVector FormationOffsetParam;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation movement functions\n// NOTE: These formation functions are WORKING and ACTIVELY USED - DO NOT REMOVE\n// They enable proper formation movement where units move to target + formation offset\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation movement functions\nNOTE: These formation functions are WORKING and ACTIVELY USED - DO NOT REMOVE\nThey enable proper formation movement where units move to target + formation offset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationOffsetParam_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationOffsetParam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventMoveToLocationWithFormation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::NewProp_FormationOffsetParam = { "FormationOffsetParam", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventMoveToLocationWithFormation_Parms, FormationOffsetParam), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationOffsetParam_MetaData), NewProp_FormationOffsetParam_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::NewProp_FormationOffsetParam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "MoveToLocationWithFormation", Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::RTSUnit_eventMoveToLocationWithFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::RTSUnit_eventMoveToLocationWithFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execMoveToLocationWithFormation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_FormationOffsetParam);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveToLocationWithFormation(Z_Param_Out_Location,Z_Param_Out_FormationOffsetParam);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function MoveToLocationWithFormation ******************************

// ********** Begin Class ARTSUnit Function OnAttackStarted ****************************************
struct RTSUnit_eventOnAttackStarted_Parms
{
	ARTSBaseActor* Target;
};
static FName NAME_ARTSUnit_OnAttackStarted = FName(TEXT("OnAttackStarted"));
void ARTSUnit::OnAttackStarted(ARTSBaseActor* Target)
{
	RTSUnit_eventOnAttackStarted_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_ARTSUnit_OnAttackStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventOnAttackStarted_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "OnAttackStarted", Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::PropPointers), sizeof(RTSUnit_eventOnAttackStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSUnit_eventOnAttackStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_OnAttackStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_OnAttackStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSUnit Function OnAttackStarted ******************************************

// ********** Begin Class ARTSUnit Function OnAttackStopped ****************************************
static FName NAME_ARTSUnit_OnAttackStopped = FName(TEXT("OnAttackStopped"));
void ARTSUnit::OnAttackStopped()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSUnit_OnAttackStopped);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSUnit_OnAttackStopped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_OnAttackStopped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "OnAttackStopped", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnAttackStopped_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_OnAttackStopped_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_OnAttackStopped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_OnAttackStopped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSUnit Function OnAttackStopped ******************************************

// ********** Begin Class ARTSUnit Function OnMovementStarted **************************************
struct RTSUnit_eventOnMovementStarted_Parms
{
	FVector Destination;
};
static FName NAME_ARTSUnit_OnMovementStarted = FName(TEXT("OnMovementStarted"));
void ARTSUnit::OnMovementStarted(FVector const& Destination)
{
	RTSUnit_eventOnMovementStarted_Parms Parms;
	Parms.Destination=Destination;
	UFunction* Func = FindFunctionChecked(NAME_ARTSUnit_OnMovementStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Note: OnTakeDamage, OnHealed, and OnDeath Blueprint events are inherited from ARTSBaseActor\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Note: OnTakeDamage, OnHealed, and OnDeath Blueprint events are inherited from ARTSBaseActor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Destination_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Destination;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::NewProp_Destination = { "Destination", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventOnMovementStarted_Parms, Destination), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Destination_MetaData), NewProp_Destination_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::NewProp_Destination,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "OnMovementStarted", Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::PropPointers), sizeof(RTSUnit_eventOnMovementStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C80800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSUnit_eventOnMovementStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_OnMovementStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_OnMovementStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSUnit Function OnMovementStarted ****************************************

// ********** Begin Class ARTSUnit Function OnMovementStopped **************************************
static FName NAME_ARTSUnit_OnMovementStopped = FName(TEXT("OnMovementStopped"));
void ARTSUnit::OnMovementStopped()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSUnit_OnMovementStopped);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSUnit_OnMovementStopped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_OnMovementStopped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "OnMovementStopped", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnMovementStopped_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_OnMovementStopped_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_OnMovementStopped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_OnMovementStopped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSUnit Function OnMovementStopped ****************************************

// ********** Begin Class ARTSUnit Function OnReachedDestination ***********************************
static FName NAME_ARTSUnit_OnReachedDestination = FName(TEXT("OnReachedDestination"));
void ARTSUnit::OnReachedDestination()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSUnit_OnReachedDestination);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSUnit_OnReachedDestination_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_OnReachedDestination_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "OnReachedDestination", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnReachedDestination_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_OnReachedDestination_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_OnReachedDestination()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_OnReachedDestination_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSUnit Function OnReachedDestination *************************************

// ********** Begin Class ARTSUnit Function OnTargetLost *******************************************
struct RTSUnit_eventOnTargetLost_Parms
{
	ARTSBaseActor* LostTarget;
};
static FName NAME_ARTSUnit_OnTargetLost = FName(TEXT("OnTargetLost"));
void ARTSUnit::OnTargetLost(ARTSBaseActor* LostTarget)
{
	RTSUnit_eventOnTargetLost_Parms Parms;
	Parms.LostTarget=LostTarget;
	UFunction* Func = FindFunctionChecked(NAME_ARTSUnit_OnTargetLost);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LostTarget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::NewProp_LostTarget = { "LostTarget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventOnTargetLost_Parms, LostTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::NewProp_LostTarget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "OnTargetLost", Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::PropPointers), sizeof(RTSUnit_eventOnTargetLost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSUnit_eventOnTargetLost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_OnTargetLost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_OnTargetLost_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSUnit Function OnTargetLost *********************************************

// ********** Begin Class ARTSUnit Function SetAIBehavior ******************************************
struct Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics
{
	struct RTSUnit_eventSetAIBehavior_Parms
	{
		ERTSAIBehavior Behavior;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI Control Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Control Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Behavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Behavior;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::NewProp_Behavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::NewProp_Behavior = { "Behavior", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventSetAIBehavior_Parms, Behavior), Z_Construct_UEnum_ArmorWars_ERTSAIBehavior, METADATA_PARAMS(0, nullptr) }; // 4189306284
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::NewProp_Behavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::NewProp_Behavior,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "SetAIBehavior", Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::RTSUnit_eventSetAIBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::RTSUnit_eventSetAIBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_SetAIBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_SetAIBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execSetAIBehavior)
{
	P_GET_ENUM(ERTSAIBehavior,Z_Param_Behavior);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAIBehavior(ERTSAIBehavior(Z_Param_Behavior));
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function SetAIBehavior ********************************************

// ********** Begin Class ARTSUnit Function SetAIDefendPosition ************************************
struct Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics
{
	struct RTSUnit_eventSetAIDefendPosition_Parms
	{
		FVector Position;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|AI" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventSetAIDefendPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::NewProp_Position,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "SetAIDefendPosition", Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::RTSUnit_eventSetAIDefendPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::RTSUnit_eventSetAIDefendPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execSetAIDefendPosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAIDefendPosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function SetAIDefendPosition **************************************

// ********** Begin Class ARTSUnit Function SetAIPatrolPoints **************************************
struct Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics
{
	struct RTSUnit_eventSetAIPatrolPoints_Parms
	{
		TArray<FVector> Points;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|AI" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventSetAIPatrolPoints_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::NewProp_Points,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "SetAIPatrolPoints", Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::RTSUnit_eventSetAIPatrolPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::RTSUnit_eventSetAIPatrolPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execSetAIPatrolPoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAIPatrolPoints(Z_Param_Out_Points);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function SetAIPatrolPoints ****************************************

// ********** Begin Class ARTSUnit Function SetFormationData ***************************************
struct Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics
{
	struct RTSUnit_eventSetFormationData_Parms
	{
		bool bInFormation;
		FVector Offset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bInFormation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInFormation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::NewProp_bInFormation_SetBit(void* Obj)
{
	((RTSUnit_eventSetFormationData_Parms*)Obj)->bInFormation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::NewProp_bInFormation = { "bInFormation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSUnit_eventSetFormationData_Parms), &Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::NewProp_bInFormation_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventSetFormationData_Parms, Offset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::NewProp_bInFormation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::NewProp_Offset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "SetFormationData", Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::RTSUnit_eventSetFormationData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::RTSUnit_eventSetFormationData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_SetFormationData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_SetFormationData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execSetFormationData)
{
	P_GET_UBOOL(Z_Param_bInFormation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Offset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFormationData(Z_Param_bInFormation,Z_Param_Out_Offset);
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function SetFormationData *****************************************

// ********** Begin Class ARTSUnit Function SetMovementState ***************************************
struct Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics
{
	struct RTSUnit_eventSetMovementState_Parms
	{
		ERTSMovementState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSUnit_eventSetMovementState_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSMovementState, METADATA_PARAMS(0, nullptr) }; // 1335892957
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "SetMovementState", Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::RTSUnit_eventSetMovementState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::RTSUnit_eventSetMovementState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSUnit_SetMovementState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_SetMovementState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execSetMovementState)
{
	P_GET_ENUM(ERTSMovementState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMovementState(ERTSMovementState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function SetMovementState *****************************************

// ********** Begin Class ARTSUnit Function StopAttacking ******************************************
struct Z_Construct_UFunction_ARTSUnit_StopAttacking_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_StopAttacking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "StopAttacking", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_StopAttacking_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_StopAttacking_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_StopAttacking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_StopAttacking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execStopAttacking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAttacking();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function StopAttacking ********************************************

// ********** Begin Class ARTSUnit Function StopMovement *******************************************
struct Z_Construct_UFunction_ARTSUnit_StopMovement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Movement" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSUnit_StopMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSUnit, nullptr, "StopMovement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSUnit_StopMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSUnit_StopMovement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSUnit_StopMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSUnit_StopMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSUnit::execStopMovement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopMovement();
	P_NATIVE_END;
}
// ********** End Class ARTSUnit Function StopMovement *********************************************

// ********** Begin Class ARTSUnit *****************************************************************
void ARTSUnit::StaticRegisterNativesARTSUnit()
{
	UClass* Class = ARTSUnit::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AttackMoveToLocation", &ARTSUnit::execAttackMoveToLocation },
		{ "AttackTarget", &ARTSUnit::execAttackTarget },
		{ "CalculateCollisionAvoidance", &ARTSUnit::execCalculateCollisionAvoidance },
		{ "CanAttackTarget", &ARTSUnit::execCanAttackTarget },
		{ "CanBeProducedByTechLevel", &ARTSUnit::execCanBeProducedByTechLevel },
		{ "CanTargetDomain", &ARTSUnit::execCanTargetDomain },
		{ "ClearCommandQueue", &ARTSUnit::execClearCommandQueue },
		{ "FindEnemiesInRange", &ARTSUnit::execFindEnemiesInRange },
		{ "GetAttackDamage", &ARTSUnit::execGetAttackDamage },
		{ "GetAttackRange", &ARTSUnit::execGetAttackRange },
		{ "GetBlackboardComponent", &ARTSUnit::execGetBlackboardComponent },
		{ "GetCommandComponent", &ARTSUnit::execGetCommandComponent },
		{ "GetCurrentCommand", &ARTSUnit::execGetCurrentCommand },
		{ "GetCurrentTarget", &ARTSUnit::execGetCurrentTarget },
		{ "GetDistanceToTarget", &ARTSUnit::execGetDistanceToTarget },
		{ "GetFormationOffset", &ARTSUnit::execGetFormationOffset },
		{ "GetMaxAttackRange", &ARTSUnit::execGetMaxAttackRange },
		{ "GetMovementSpeed", &ARTSUnit::execGetMovementSpeed },
		{ "GetMovementState", &ARTSUnit::execGetMovementState },
		{ "GetNearbyUnits", &ARTSUnit::execGetNearbyUnits },
		{ "GetTotalDamagePerSecond", &ARTSUnit::execGetTotalDamagePerSecond },
		{ "GetUnitDomain", &ARTSUnit::execGetUnitDomain },
		{ "GetWeaponController", &ARTSUnit::execGetWeaponController },
		{ "HasCommands", &ARTSUnit::execHasCommands },
		{ "HasWeapons", &ARTSUnit::execHasWeapons },
		{ "IsAdvancedUnit", &ARTSUnit::execIsAdvancedUnit },
		{ "IsAirUnit", &ARTSUnit::execIsAirUnit },
		{ "IsAttacking", &ARTSUnit::execIsAttacking },
		{ "IsInAttackRange", &ARTSUnit::execIsInAttackRange },
		{ "IsInFormation", &ARTSUnit::execIsInFormation },
		{ "IsLandUnit", &ARTSUnit::execIsLandUnit },
		{ "IsMoving", &ARTSUnit::execIsMoving },
		{ "IsSeaUnit", &ARTSUnit::execIsSeaUnit },
		{ "IsSubnauticalUnit", &ARTSUnit::execIsSubnauticalUnit },
		{ "IssueAttackCommand", &ARTSUnit::execIssueAttackCommand },
		{ "IssueCommand", &ARTSUnit::execIssueCommand },
		{ "IssueMoveCommand", &ARTSUnit::execIssueMoveCommand },
		{ "IssueStopCommand", &ARTSUnit::execIssueStopCommand },
		{ "JoinFormation", &ARTSUnit::execJoinFormation },
		{ "LeaveFormation", &ARTSUnit::execLeaveFormation },
		{ "MoveToLocation", &ARTSUnit::execMoveToLocation },
		{ "MoveToLocationSynchronized", &ARTSUnit::execMoveToLocationSynchronized },
		{ "MoveToLocationWithFormation", &ARTSUnit::execMoveToLocationWithFormation },
		{ "SetAIBehavior", &ARTSUnit::execSetAIBehavior },
		{ "SetAIDefendPosition", &ARTSUnit::execSetAIDefendPosition },
		{ "SetAIPatrolPoints", &ARTSUnit::execSetAIPatrolPoints },
		{ "SetFormationData", &ARTSUnit::execSetFormationData },
		{ "SetMovementState", &ARTSUnit::execSetMovementState },
		{ "StopAttacking", &ARTSUnit::execStopAttacking },
		{ "StopMovement", &ARTSUnit::execStopMovement },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSUnit;
UClass* ARTSUnit::GetPrivateStaticClass()
{
	using TClass = ARTSUnit;
	if (!Z_Registration_Info_UClass_ARTSUnit.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSUnit"),
			Z_Registration_Info_UClass_ARTSUnit.InnerSingleton,
			StaticRegisterNativesARTSUnit,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSUnit.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSUnit_NoRegister()
{
	return ARTSUnit::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Unit class for all movable RTS units\n * Inherits from APawn to support AI controller possession and movement components\n * Includes shared functionality from RTSBaseActor through composition\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "RTSUnit.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unit class for all movable RTS units\nInherits from APawn to support AI controller possession and movement components\nIncludes shared functionality from RTSBaseActor through composition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArmorComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TacticalAIComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponController_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlackboardComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMovementChanged_MetaData[] = {
		{ "Category", "RTS|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Note: Shared properties from RTSBaseActor are inherited (ActorType, TeamID, TechLevel, Health, Selection, GameplayTags, Delegates)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Note: Shared properties from RTSBaseActor are inherited (ActorType, TeamID, TechLevel, Health, Selection, GameplayTags, Delegates)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnitDomain_MetaData[] = {
		{ "Category", "RTS|Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unit domain (Land, Air, Sea, Subnautical)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unit domain (Land, Air, Sea, Subnautical)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement speed\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TurnRate_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Turn rate (degrees per second)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Turn rate (degrees per second)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Acceleration_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Acceleration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Acceleration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Deceleration_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Deceleration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deceleration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TurningAngleTolerance_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMax", "45.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Angle tolerance for forward movement (degrees) - unit will turn if facing direction differs by more than this\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Angle tolerance for forward movement (degrees) - unit will turn if facing direction differs by more than this" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArrivalDistance_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance tolerance for reaching destination (cm)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance tolerance for reaching destination (cm)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SynchronizedMovementSpeed_MetaData[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Synchronized movement speed (overrides MovementSpeed when in formation)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Synchronized movement speed (overrides MovementSpeed when in formation)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSynchronizedMovement_MetaData[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit is using synchronized movement\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit is using synchronized movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRange_MetaData[] = {
		{ "Category", "RTS|Vision" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Vision range\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vision range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current movement target location\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current movement target location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMoving_MetaData[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether the unit is currently moving\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the unit is currently moving" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementState_MetaData[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current movement state for better movement control\n// NOTE: This movement state tracking is WORKING and ACTIVELY USED - DO NOT REMOVE\n// Provides proper RTS movement behavior with turn-in-place and gradual turning\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current movement state for better movement control\nNOTE: This movement state tracking is WORKING and ACTIVELY USED - DO NOT REMOVE\nProvides proper RTS movement behavior with turn-in-place and gradual turning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInFormation_MetaData[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation-related properties\n// NOTE: This formation system is WORKING and ACTIVELY USED - DO NOT REMOVE\n// Enables proper formation movement where units move to target + formation offset\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation-related properties\nNOTE: This formation system is WORKING and ACTIVELY USED - DO NOT REMOVE\nEnables proper formation movement where units move to target + formation offset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationOffset_MetaData[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionAvoidanceRadius_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision avoidance properties\n// NOTE: This collision avoidance system is WORKING and ACTIVELY USED - DO NOT REMOVE\n// Prevents units from moving through each other and provides smooth avoidance\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision avoidance properties\nNOTE: This collision avoidance system is WORKING and ACTIVELY USED - DO NOT REMOVE\nPrevents units from moving through each other and provides smooth avoidance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionAvoidanceStrength_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAvoidanceTime_MetaData[] = {
		{ "Category", "RTS|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Time tracking for avoiding rapid direction changes\n// NOTE: This avoidance cooldown system is WORKING and ACTIVELY USED - DO NOT REMOVE\n// Prevents oscillation and rapid direction changes that cause movement issues\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time tracking for avoiding rapid direction changes\nNOTE: This avoidance cooldown system is WORKING and ACTIVELY USED - DO NOT REMOVE\nPrevents oscillation and rapid direction changes that cause movement issues" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvoidanceCooldown_MetaData[] = {
		{ "Category", "RTS|Movement" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/RTSUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ArmorComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AIComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TacticalAIComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponController;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CommandComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BlackboardComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnFireComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMovementChanged;
	static const UECodeGen_Private::FBytePropertyParams NewProp_UnitDomain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UnitDomain;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TurnRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Acceleration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Deceleration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TurningAngleTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ArrivalDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SynchronizedMovementSpeed;
	static void NewProp_bUseSynchronizedMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSynchronizedMovement;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static void NewProp_bIsMoving_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMoving;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MovementState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MovementState;
	static void NewProp_bIsInFormation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInFormation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionAvoidanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionAvoidanceStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAvoidanceTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvoidanceCooldown;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSUnit_AttackMoveToLocation, "AttackMoveToLocation" }, // 246233667
		{ &Z_Construct_UFunction_ARTSUnit_AttackTarget, "AttackTarget" }, // 2676021764
		{ &Z_Construct_UFunction_ARTSUnit_CalculateCollisionAvoidance, "CalculateCollisionAvoidance" }, // 610217798
		{ &Z_Construct_UFunction_ARTSUnit_CanAttackTarget, "CanAttackTarget" }, // 4137451336
		{ &Z_Construct_UFunction_ARTSUnit_CanBeProducedByTechLevel, "CanBeProducedByTechLevel" }, // 4096360708
		{ &Z_Construct_UFunction_ARTSUnit_CanTargetDomain, "CanTargetDomain" }, // 910563322
		{ &Z_Construct_UFunction_ARTSUnit_ClearCommandQueue, "ClearCommandQueue" }, // 2978849752
		{ &Z_Construct_UFunction_ARTSUnit_FindEnemiesInRange, "FindEnemiesInRange" }, // 3114374245
		{ &Z_Construct_UFunction_ARTSUnit_GetAttackDamage, "GetAttackDamage" }, // 678449494
		{ &Z_Construct_UFunction_ARTSUnit_GetAttackRange, "GetAttackRange" }, // 148438852
		{ &Z_Construct_UFunction_ARTSUnit_GetBlackboardComponent, "GetBlackboardComponent" }, // 938524097
		{ &Z_Construct_UFunction_ARTSUnit_GetCommandComponent, "GetCommandComponent" }, // 3760468777
		{ &Z_Construct_UFunction_ARTSUnit_GetCurrentCommand, "GetCurrentCommand" }, // 241170388
		{ &Z_Construct_UFunction_ARTSUnit_GetCurrentTarget, "GetCurrentTarget" }, // 1928127323
		{ &Z_Construct_UFunction_ARTSUnit_GetDistanceToTarget, "GetDistanceToTarget" }, // 2517826412
		{ &Z_Construct_UFunction_ARTSUnit_GetFormationOffset, "GetFormationOffset" }, // 443121563
		{ &Z_Construct_UFunction_ARTSUnit_GetMaxAttackRange, "GetMaxAttackRange" }, // 2004650831
		{ &Z_Construct_UFunction_ARTSUnit_GetMovementSpeed, "GetMovementSpeed" }, // 1423408813
		{ &Z_Construct_UFunction_ARTSUnit_GetMovementState, "GetMovementState" }, // 3375740556
		{ &Z_Construct_UFunction_ARTSUnit_GetNearbyUnits, "GetNearbyUnits" }, // 3118406231
		{ &Z_Construct_UFunction_ARTSUnit_GetTotalDamagePerSecond, "GetTotalDamagePerSecond" }, // 2578389595
		{ &Z_Construct_UFunction_ARTSUnit_GetUnitDomain, "GetUnitDomain" }, // 4154996752
		{ &Z_Construct_UFunction_ARTSUnit_GetWeaponController, "GetWeaponController" }, // 260913019
		{ &Z_Construct_UFunction_ARTSUnit_HasCommands, "HasCommands" }, // 482314431
		{ &Z_Construct_UFunction_ARTSUnit_HasWeapons, "HasWeapons" }, // 3488590671
		{ &Z_Construct_UFunction_ARTSUnit_IsAdvancedUnit, "IsAdvancedUnit" }, // 1983189358
		{ &Z_Construct_UFunction_ARTSUnit_IsAirUnit, "IsAirUnit" }, // 3958572332
		{ &Z_Construct_UFunction_ARTSUnit_IsAttacking, "IsAttacking" }, // 2898189131
		{ &Z_Construct_UFunction_ARTSUnit_IsInAttackRange, "IsInAttackRange" }, // 2404317870
		{ &Z_Construct_UFunction_ARTSUnit_IsInFormation, "IsInFormation" }, // 2584597090
		{ &Z_Construct_UFunction_ARTSUnit_IsLandUnit, "IsLandUnit" }, // 614374353
		{ &Z_Construct_UFunction_ARTSUnit_IsMoving, "IsMoving" }, // 820141163
		{ &Z_Construct_UFunction_ARTSUnit_IsSeaUnit, "IsSeaUnit" }, // 2338143706
		{ &Z_Construct_UFunction_ARTSUnit_IsSubnauticalUnit, "IsSubnauticalUnit" }, // 3987875571
		{ &Z_Construct_UFunction_ARTSUnit_IssueAttackCommand, "IssueAttackCommand" }, // 1364122185
		{ &Z_Construct_UFunction_ARTSUnit_IssueCommand, "IssueCommand" }, // 1233368073
		{ &Z_Construct_UFunction_ARTSUnit_IssueMoveCommand, "IssueMoveCommand" }, // 3848682427
		{ &Z_Construct_UFunction_ARTSUnit_IssueStopCommand, "IssueStopCommand" }, // 1556042118
		{ &Z_Construct_UFunction_ARTSUnit_JoinFormation, "JoinFormation" }, // 2043977412
		{ &Z_Construct_UFunction_ARTSUnit_LeaveFormation, "LeaveFormation" }, // 3361962774
		{ &Z_Construct_UFunction_ARTSUnit_MoveToLocation, "MoveToLocation" }, // 1825595758
		{ &Z_Construct_UFunction_ARTSUnit_MoveToLocationSynchronized, "MoveToLocationSynchronized" }, // 1982418300
		{ &Z_Construct_UFunction_ARTSUnit_MoveToLocationWithFormation, "MoveToLocationWithFormation" }, // 1909026389
		{ &Z_Construct_UFunction_ARTSUnit_OnAttackStarted, "OnAttackStarted" }, // 1892971684
		{ &Z_Construct_UFunction_ARTSUnit_OnAttackStopped, "OnAttackStopped" }, // 3719710676
		{ &Z_Construct_UFunction_ARTSUnit_OnMovementStarted, "OnMovementStarted" }, // 41770449
		{ &Z_Construct_UFunction_ARTSUnit_OnMovementStopped, "OnMovementStopped" }, // 2042756078
		{ &Z_Construct_UFunction_ARTSUnit_OnReachedDestination, "OnReachedDestination" }, // 1198669549
		{ &Z_Construct_UFunction_ARTSUnit_OnTargetLost, "OnTargetLost" }, // 2883140634
		{ &Z_Construct_UFunction_ARTSUnit_SetAIBehavior, "SetAIBehavior" }, // 1012768092
		{ &Z_Construct_UFunction_ARTSUnit_SetAIDefendPosition, "SetAIDefendPosition" }, // 337226983
		{ &Z_Construct_UFunction_ARTSUnit_SetAIPatrolPoints, "SetAIPatrolPoints" }, // 3666759874
		{ &Z_Construct_UFunction_ARTSUnit_SetFormationData, "SetFormationData" }, // 90979911
		{ &Z_Construct_UFunction_ARTSUnit_SetMovementState, "SetMovementState" }, // 3141722313
		{ &Z_Construct_UFunction_ARTSUnit_StopAttacking, "StopAttacking" }, // 3322312449
		{ &Z_Construct_UFunction_ARTSUnit_StopMovement, "StopMovement" }, // 3431718983
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSUnit>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_CollisionComponent = { "CollisionComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, CollisionComponent), Z_Construct_UClass_UCapsuleComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComponent_MetaData), NewProp_CollisionComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_ArmorComponent = { "ArmorComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, ArmorComponent), Z_Construct_UClass_URTSArmorComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArmorComponent_MetaData), NewProp_ArmorComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_AIComponent = { "AIComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, AIComponent), Z_Construct_UClass_URTSUnitAIComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIComponent_MetaData), NewProp_AIComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_TacticalAIComponent = { "TacticalAIComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, TacticalAIComponent), Z_Construct_UClass_URTSTacticalAIComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TacticalAIComponent_MetaData), NewProp_TacticalAIComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_WeaponController = { "WeaponController", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, WeaponController), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponController_MetaData), NewProp_WeaponController_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_CommandComponent = { "CommandComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, CommandComponent), Z_Construct_UClass_URTSCommandComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandComponent_MetaData), NewProp_CommandComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_BlackboardComponent = { "BlackboardComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, BlackboardComponent), Z_Construct_UClass_URTSBlackboardComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlackboardComponent_MetaData), NewProp_BlackboardComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_ReturnFireComponent = { "ReturnFireComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, ReturnFireComponent), Z_Construct_UClass_URTSReturnFireComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireComponent_MetaData), NewProp_ReturnFireComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_OnMovementChanged = { "OnMovementChanged", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, OnMovementChanged), Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMovementChanged_MetaData), NewProp_OnMovementChanged_MetaData) }; // 3281655495
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_UnitDomain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_UnitDomain = { "UnitDomain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, UnitDomain), Z_Construct_UEnum_ArmorWars_ERTSUnitDomain, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnitDomain_MetaData), NewProp_UnitDomain_MetaData) }; // 3001890894
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_MovementSpeed = { "MovementSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, MovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_TurnRate = { "TurnRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, TurnRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TurnRate_MetaData), NewProp_TurnRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_Acceleration = { "Acceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, Acceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Acceleration_MetaData), NewProp_Acceleration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_Deceleration = { "Deceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, Deceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Deceleration_MetaData), NewProp_Deceleration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_TurningAngleTolerance = { "TurningAngleTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, TurningAngleTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TurningAngleTolerance_MetaData), NewProp_TurningAngleTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_ArrivalDistance = { "ArrivalDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, ArrivalDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArrivalDistance_MetaData), NewProp_ArrivalDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_SynchronizedMovementSpeed = { "SynchronizedMovementSpeed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, SynchronizedMovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SynchronizedMovementSpeed_MetaData), NewProp_SynchronizedMovementSpeed_MetaData) };
void Z_Construct_UClass_ARTSUnit_Statics::NewProp_bUseSynchronizedMovement_SetBit(void* Obj)
{
	((ARTSUnit*)Obj)->bUseSynchronizedMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_bUseSynchronizedMovement = { "bUseSynchronizedMovement", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSUnit), &Z_Construct_UClass_ARTSUnit_Statics::NewProp_bUseSynchronizedMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSynchronizedMovement_MetaData), NewProp_bUseSynchronizedMovement_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_VisionRange = { "VisionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, VisionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRange_MetaData), NewProp_VisionRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
void Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsMoving_SetBit(void* Obj)
{
	((ARTSUnit*)Obj)->bIsMoving = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsMoving = { "bIsMoving", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSUnit), &Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsMoving_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMoving_MetaData), NewProp_bIsMoving_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_MovementState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_MovementState = { "MovementState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, MovementState), Z_Construct_UEnum_ArmorWars_ERTSMovementState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementState_MetaData), NewProp_MovementState_MetaData) }; // 1335892957
void Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsInFormation_SetBit(void* Obj)
{
	((ARTSUnit*)Obj)->bIsInFormation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsInFormation = { "bIsInFormation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSUnit), &Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsInFormation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInFormation_MetaData), NewProp_bIsInFormation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_FormationOffset = { "FormationOffset", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, FormationOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationOffset_MetaData), NewProp_FormationOffset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_CollisionAvoidanceRadius = { "CollisionAvoidanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, CollisionAvoidanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionAvoidanceRadius_MetaData), NewProp_CollisionAvoidanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_CollisionAvoidanceStrength = { "CollisionAvoidanceStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, CollisionAvoidanceStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionAvoidanceStrength_MetaData), NewProp_CollisionAvoidanceStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_LastAvoidanceTime = { "LastAvoidanceTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, LastAvoidanceTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAvoidanceTime_MetaData), NewProp_LastAvoidanceTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSUnit_Statics::NewProp_AvoidanceCooldown = { "AvoidanceCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSUnit, AvoidanceCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvoidanceCooldown_MetaData), NewProp_AvoidanceCooldown_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_CollisionComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_ArmorComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_AIComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_TacticalAIComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_WeaponController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_CommandComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_BlackboardComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_ReturnFireComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_OnMovementChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_UnitDomain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_UnitDomain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_TurnRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_Acceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_Deceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_TurningAngleTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_ArrivalDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_SynchronizedMovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_bUseSynchronizedMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_VisionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsMoving,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_MovementState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_MovementState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_bIsInFormation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_FormationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_CollisionAvoidanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_CollisionAvoidanceStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_LastAvoidanceTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSUnit_Statics::NewProp_AvoidanceCooldown,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSUnit_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSUnit_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ARTSBaseActor,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSUnit_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSUnit_Statics::ClassParams = {
	&ARTSUnit::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSUnit_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSUnit_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSUnit_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSUnit_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSUnit()
{
	if (!Z_Registration_Info_UClass_ARTSUnit.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSUnit.OuterSingleton, Z_Construct_UClass_ARTSUnit_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSUnit.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSUnit);
ARTSUnit::~ARTSUnit() {}
// ********** End Class ARTSUnit *******************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSMovementState_StaticEnum, TEXT("ERTSMovementState"), &Z_Registration_Info_UEnum_ERTSMovementState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1335892957U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSUnit, ARTSUnit::StaticClass, TEXT("ARTSUnit"), &Z_Registration_Info_UClass_ARTSUnit, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSUnit), 2743239063U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h__Script_ArmorWars_749521560(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSUnit_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
