#include "RTSReturnFireSystem.h"
#include "RTSUnit.h"
#include "RTSWeaponController.h"
#include "RTSTeamManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

URTSReturnFireComponent::URTSReturnFireComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f;
    
    bEnableReturnFire = true;
    bEnableDebugLogging = false;
}

void URTSReturnFireComponent::BeginPlay()
{
    Super::BeginPlay();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: BeginPlay for %s"), 
            GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

void URTSReturnFireComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (bEnableReturnFire)
    {
        ProcessReturnFire(DeltaTime);
    }
}

void URTSReturnFireComponent::StartReturnFire(ARTSBaseActor* Attacker)
{
    if (!bEnableReturnFire || !CanReturnFire())
    {
        return;
    }
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Check cooldown
    if (CurrentTime - State.LastReturnFireTime < Config.ReturnFireCooldown)
    {
        return;
    }
    
    // Register attacker if provided
    if (Attacker)
    {
        RegisterAttacker(Attacker);
    }
    
    // Select target
    ARTSBaseActor* Target = SelectReturnFireTarget();
    if (!Target)
    {
        return;
    }
    
    // Start return fire
    State.bIsReturningFire = true;
    State.CurrentTarget = Target;
    State.ReturnFireStartTime = CurrentTime;
    State.LastReturnFireTime = CurrentTime;
    
    // Check if unit is moving
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (OwnerUnit)
    {
        State.bWasMovingWhenFiring = !OwnerUnit->GetVelocity().IsNearlyZero();
        if (State.bWasMovingWhenFiring)
        {
            // Store original move target for later restoration
            State.OriginalMoveTarget = OwnerUnit->GetActorLocation() + OwnerUnit->GetVelocity().GetSafeNormal() * 1000.0f;
        }
    }
    
    // Broadcast event
    OnReturnFireStarted.Broadcast(Target);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: Started return fire against %s"), 
            Target ? *Target->GetName() : TEXT("Unknown"));
    }
}

void URTSReturnFireComponent::StopReturnFire()
{
    if (!State.bIsReturningFire)
    {
        return;
    }
    
    State.bIsReturningFire = false;
    State.CurrentTarget = nullptr;
    State.bWasMovingWhenFiring = false;
    
    // Broadcast event
    OnReturnFireStopped.Broadcast();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: Stopped return fire"));
    }
}

void URTSReturnFireComponent::ProcessReturnFire(float DeltaTime)
{
    if (!bEnableReturnFire)
    {
        return;
    }
    
    // Clean up old attackers
    CleanupOldAttackers();
    
    // Check if we should start return fire
    if (!State.bIsReturningFire && ShouldReturnFire())
    {
        StartReturnFire();
    }
    
    // Process active return fire
    if (State.bIsReturningFire)
    {
        UpdateReturnFireState(DeltaTime);
    }
}

ARTSBaseActor* URTSReturnFireComponent::SelectReturnFireTarget()
{
    ARTSBaseActor* SelectedTarget = nullptr;
    
    switch (Config.TargetPriority)
    {
        case ERTSReturnFirePriority::NearestEnemy:
            SelectedTarget = FindNearestEnemy();
            break;
            
        case ERTSReturnFirePriority::AttackingEnemy:
            SelectedTarget = FindAttackingEnemy();
            break;
            
        case ERTSReturnFirePriority::WeakestEnemy:
            SelectedTarget = FindWeakestEnemy();
            break;
            
        case ERTSReturnFirePriority::StrongestThreat:
            SelectedTarget = FindStrongestThreat();
            break;
            
        case ERTSReturnFirePriority::LastAttacker:
            SelectedTarget = GetMostRecentAttacker();
            break;
            
        default:
            SelectedTarget = FindNearestEnemy();
            break;
    }
    
    // Validate selected target
    if (!IsValidTarget(SelectedTarget))
    {
        // Fallback to nearest enemy
        SelectedTarget = FindNearestEnemy();
    }
    
    return SelectedTarget;
}

bool URTSReturnFireComponent::ShouldSwitchTarget(ARTSBaseActor* NewTarget)
{
    if (!Config.bSwitchTargetsOpportunistically)
    {
        return false;
    }
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Check switch cooldown
    if (CurrentTime - State.LastTargetSwitchTime < Config.TargetSwitchCooldown)
    {
        return false;
    }
    
    ARTSBaseActor* CurrentTarget = State.CurrentTarget.Get();
    if (!CurrentTarget || !IsValidTarget(CurrentTarget))
    {
        return true; // Current target is invalid, switch
    }
    
    if (!NewTarget || !IsValidTarget(NewTarget))
    {
        return false; // New target is invalid
    }
    
    // Calculate priorities
    float CurrentPriority = CalculateTargetPriority(CurrentTarget);
    float NewPriority = CalculateTargetPriority(NewTarget);
    
    // Switch if new target has significantly higher priority
    return NewPriority > CurrentPriority * 1.2f; // 20% threshold
}

void URTSReturnFireComponent::SetReturnFireTarget(ARTSBaseActor* Target)
{
    ARTSBaseActor* OldTarget = State.CurrentTarget.Get();
    
    if (OldTarget != Target)
    {
        State.CurrentTarget = Target;
        State.LastTargetSwitchTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
        
        OnTargetChanged.Broadcast(OldTarget, Target);
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: Target changed from %s to %s"),
                OldTarget ? *OldTarget->GetName() : TEXT("None"),
                Target ? *Target->GetName() : TEXT("None"));
        }
    }
}

void URTSReturnFireComponent::RegisterAttacker(ARTSBaseActor* Attacker)
{
    if (!Attacker || !IsValidTarget(Attacker))
    {
        return;
    }
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Add to recent attackers list
    State.RecentAttackers.AddUnique(Attacker);
    State.AttackerTimestamps.Add(Attacker, CurrentTime);
    
    OnAttackerRegistered.Broadcast(Attacker);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: Registered attacker %s"), *Attacker->GetName());
    }
}

void URTSReturnFireComponent::CleanupOldAttackers()
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Remove attackers older than memory duration
    State.RecentAttackers.RemoveAll([this, CurrentTime](const TSoftObjectPtr<ARTSBaseActor>& AttackerPtr)
    {
        ARTSBaseActor* Attacker = AttackerPtr.Get();
        if (!Attacker)
        {
            return true; // Remove invalid references
        }

        float* AttackTime = State.AttackerTimestamps.Find(AttackerPtr);
        if (!AttackTime)
        {
            return true; // Remove if no timestamp found
        }

        bool bShouldRemove = (CurrentTime - *AttackTime) > Config.AttackerMemoryDuration;
        if (bShouldRemove)
        {
            State.AttackerTimestamps.Remove(AttackerPtr);
        }

        return bShouldRemove;
    });
}

TArray<ARTSBaseActor*> URTSReturnFireComponent::GetRecentAttackers() const
{
    TArray<ARTSBaseActor*> Attackers;
    
    for (const TSoftObjectPtr<ARTSBaseActor>& AttackerPtr : State.RecentAttackers)
    {
        if (ARTSBaseActor* Attacker = AttackerPtr.Get())
        {
            Attackers.Add(Attacker);
        }
    }
    
    return Attackers;
}

ARTSBaseActor* URTSReturnFireComponent::GetMostRecentAttacker() const
{
    ARTSBaseActor* MostRecentAttacker = nullptr;
    float MostRecentTime = 0.0f;
    
    for (const auto& AttackerPair : State.AttackerTimestamps)
    {
        if (ARTSBaseActor* Attacker = AttackerPair.Key.Get())
        {
            if (AttackerPair.Value > MostRecentTime && IsValidTarget(Attacker))
            {
                MostRecentTime = AttackerPair.Value;
                MostRecentAttacker = Attacker;
            }
        }
    }
    
    return MostRecentAttacker;
}

bool URTSReturnFireComponent::CanReturnFire() const
{
    if (!bEnableReturnFire)
    {
        return false;
    }

    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OwnerUnit->IsAlive())
    {
        return false;
    }

    URTSWeaponController* WeaponController = GetWeaponController();
    if (!WeaponController || !WeaponController->HasWeapons())
    {
        return false;
    }

    return true;
}

bool URTSReturnFireComponent::ShouldReturnFire() const
{
    if (!CanReturnFire())
    {
        return false;
    }

    // Check behavior setting
    if (Config.Behavior == ERTSReturnFireBehavior::None)
    {
        return false;
    }

    // Check if we have recent attackers or nearby enemies
    bool bHasAttackers = State.RecentAttackers.Num() > 0;
    bool bHasNearbyEnemies = FindNearestEnemy() != nullptr;

    switch (Config.Behavior)
    {
        case ERTSReturnFireBehavior::Defensive:
            return bHasAttackers;

        case ERTSReturnFireBehavior::Opportunistic:
            return bHasAttackers || bHasNearbyEnemies;

        case ERTSReturnFireBehavior::Aggressive:
            return bHasNearbyEnemies;

        case ERTSReturnFireBehavior::AlwaysReturn:
            return true;

        default:
            return false;
    }
}

float URTSReturnFireComponent::GetMovementSpeedMultiplier() const
{
    if (!State.bIsReturningFire || !Config.bSlowDownWhileFiring)
    {
        return 1.0f;
    }

    return Config.MovementSpeedMultiplier;
}

bool URTSReturnFireComponent::ShouldStopToFire() const
{
    return State.bIsReturningFire && Config.bStopToFireAccurately;
}

void URTSReturnFireComponent::HandleMovementWhileFiring()
{
    if (!State.bIsReturningFire || !Config.bAllowReturnFireWhileMoving)
    {
        return;
    }

    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        return;
    }

    // Apply movement speed multiplier
    if (Config.bSlowDownWhileFiring)
    {
        // This would need to be integrated with the movement system
        // For now, just log the intention
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: Applying movement speed multiplier %.2f"),
                Config.MovementSpeedMultiplier);
        }
    }
}

ARTSUnit* URTSReturnFireComponent::GetOwnerUnit() const
{
    return Cast<ARTSUnit>(GetOwner());
}

URTSWeaponController* URTSReturnFireComponent::GetWeaponController() const
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    return OwnerUnit ? OwnerUnit->GetWeaponController() : nullptr;
}

float URTSReturnFireComponent::GetEffectiveReturnFireRange() const
{
    if (Config.bUseWeaponRange)
    {
        ARTSUnit* OwnerUnit = GetOwnerUnit();
        if (OwnerUnit)
        {
            float WeaponRange = OwnerUnit->GetMaxAttackRange();
            if (WeaponRange > 0.0f)
            {
                return WeaponRange;
            }
        }
    }

    return Config.MaxReturnFireRange;
}

void URTSReturnFireComponent::UpdateReturnFireState(float DeltaTime)
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Check if return fire duration has expired
    if (Config.ReturnFireDuration > 0.0f &&
        (CurrentTime - State.ReturnFireStartTime) > Config.ReturnFireDuration)
    {
        StopReturnFire();
        return;
    }

    // Process components
    ProcessTargetSelection();
    ProcessWeaponFiring();
    ProcessMovementAdjustment();
}

void URTSReturnFireComponent::ProcessTargetSelection()
{
    // Check if current target is still valid
    ARTSBaseActor* CurrentTarget = State.CurrentTarget.Get();
    if (!IsValidTarget(CurrentTarget))
    {
        // Select new target
        ARTSBaseActor* NewTarget = SelectReturnFireTarget();
        if (NewTarget)
        {
            SetReturnFireTarget(NewTarget);
        }
        else
        {
            StopReturnFire();
            return;
        }
    }

    // Check for better targets if opportunistic switching is enabled
    if (Config.bSwitchTargetsOpportunistically)
    {
        ARTSBaseActor* BetterTarget = SelectReturnFireTarget();
        if (BetterTarget && ShouldSwitchTarget(BetterTarget))
        {
            SetReturnFireTarget(BetterTarget);
        }
    }
}

void URTSReturnFireComponent::ProcessWeaponFiring()
{
    ARTSBaseActor* Target = State.CurrentTarget.Get();
    if (!Target)
    {
        return;
    }

    URTSWeaponController* WeaponController = GetWeaponController();
    if (!WeaponController)
    {
        return;
    }

    // Fire at target
    WeaponController->SetTarget(Target);
    WeaponController->StartFiring();

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSReturnFireComponent: Firing at target %s"), *Target->GetName());
    }
}

void URTSReturnFireComponent::ProcessMovementAdjustment()
{
    HandleMovementWhileFiring();
}

ARTSBaseActor* URTSReturnFireComponent::FindNearestEnemy()
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OwnerUnit->GetWorld())
    {
        return nullptr;
    }

    URTSTeamManager* TeamManager = OwnerUnit->GetWorld()->GetSubsystem<URTSTeamManager>();
    if (!TeamManager)
    {
        return nullptr;
    }

    float EffectiveRange = GetEffectiveReturnFireRange();
    return TeamManager->FindNearestEnemy(OwnerUnit->GetTeamID(), OwnerUnit->GetActorLocation(), EffectiveRange);
}

ARTSBaseActor* URTSReturnFireComponent::FindAttackingEnemy()
{
    // Prioritize recent attackers
    ARTSBaseActor* MostRecentAttacker = GetMostRecentAttacker();
    if (IsTargetInRange(MostRecentAttacker))
    {
        return MostRecentAttacker;
    }

    // Fallback to nearest enemy
    return FindNearestEnemy();
}

ARTSBaseActor* URTSReturnFireComponent::FindWeakestEnemy()
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OwnerUnit->GetWorld())
    {
        return nullptr;
    }

    URTSTeamManager* TeamManager = OwnerUnit->GetWorld()->GetSubsystem<URTSTeamManager>();
    if (!TeamManager)
    {
        return nullptr;
    }

    float EffectiveRange = GetEffectiveReturnFireRange();
    TArray<ARTSBaseActor*> EnemiesInRange = TeamManager->FindEnemiesInRange(
        OwnerUnit->GetTeamID(), OwnerUnit->GetActorLocation(), EffectiveRange);

    ARTSBaseActor* WeakestEnemy = nullptr;
    float LowestHealth = TNumericLimits<float>::Max();

    for (ARTSBaseActor* Enemy : EnemiesInRange)
    {
        if (IsValidTarget(Enemy))
        {
            float HealthPercentage = Enemy->GetCurrentHealth() / Enemy->GetMaxHealth();
            if (HealthPercentage < LowestHealth)
            {
                LowestHealth = HealthPercentage;
                WeakestEnemy = Enemy;
            }
        }
    }

    return WeakestEnemy;
}

ARTSBaseActor* URTSReturnFireComponent::FindStrongestThreat()
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OwnerUnit->GetWorld())
    {
        return nullptr;
    }

    URTSTeamManager* TeamManager = OwnerUnit->GetWorld()->GetSubsystem<URTSTeamManager>();
    if (!TeamManager)
    {
        return nullptr;
    }

    float EffectiveRange = GetEffectiveReturnFireRange();
    TArray<ARTSBaseActor*> EnemiesInRange = TeamManager->FindEnemiesInRange(
        OwnerUnit->GetTeamID(), OwnerUnit->GetActorLocation(), EffectiveRange);

    ARTSBaseActor* StrongestThreat = nullptr;
    float HighestThreat = 0.0f;

    for (ARTSBaseActor* Enemy : EnemiesInRange)
    {
        if (IsValidTarget(Enemy))
        {
            float ThreatLevel = CalculateTargetPriority(Enemy);
            if (ThreatLevel > HighestThreat)
            {
                HighestThreat = ThreatLevel;
                StrongestThreat = Enemy;
            }
        }
    }

    return StrongestThreat;
}

float URTSReturnFireComponent::CalculateTargetPriority(ARTSBaseActor* Target)
{
    if (!IsValidTarget(Target))
    {
        return 0.0f;
    }

    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        return 0.0f;
    }

    float Priority = 1.0f;

    // Distance factor (closer = higher priority)
    float Distance = FVector::Dist(OwnerUnit->GetActorLocation(), Target->GetActorLocation());
    float EffectiveRange = GetEffectiveReturnFireRange();
    if (EffectiveRange > 0.0f)
    {
        Priority += (1.0f - (Distance / EffectiveRange)) * 2.0f;
    }

    // Health factor (lower health = higher priority for finishing off)
    if (Target->GetMaxHealth() > 0.0f)
    {
        float HealthRatio = Target->GetCurrentHealth() / Target->GetMaxHealth();
        Priority += (1.0f - HealthRatio) * 1.5f;
    }

    // Attacker bonus
    if (State.AttackerTimestamps.Contains(Target))
    {
        Priority += 3.0f;
    }

    return Priority;
}

bool URTSReturnFireComponent::IsValidTarget(ARTSBaseActor* Target) const
{
    if (!Target || !Target->IsAlive())
    {
        return false;
    }

    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        return false;
    }

    // Check if it's an enemy
    UWorld* World = OwnerUnit->GetWorld();
    if (World)
    {
        if (URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>())
        {
            return TeamManager->AreTeamsEnemies(OwnerUnit->GetTeamID(), Target->GetTeamID());
        }
    }

    return false;
}

bool URTSReturnFireComponent::IsTargetInRange(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return false;
    }

    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        return false;
    }

    float Distance = FVector::Dist(OwnerUnit->GetActorLocation(), Target->GetActorLocation());
    return Distance <= GetEffectiveReturnFireRange();
}

bool URTSReturnFireComponent::CanEngageTarget(ARTSBaseActor* Target) const
{
    return IsValidTarget(Target) && IsTargetInRange(Target);
}
