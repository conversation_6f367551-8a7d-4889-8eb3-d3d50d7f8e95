// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSBaseActor.h"

#ifdef ARMORWARS_RTSBaseActor_generated_h
#error "RTSBaseActor.generated.h already included, missing '#pragma once' in RTSBaseActor.h"
#endif
#define ARMORWARS_RTSBaseActor_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ARTSBaseActor;
enum class ERTSActorType : uint8;
enum class ERTSTechLevel : uint8;
struct FGameplayTag;
struct FGameplayTagContainer;

// ********** Begin Delegate FOnHealthChanged ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_237_DELEGATE \
static void FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float NewHealth, float MaxHealth);


// ********** End Delegate FOnHealthChanged ********************************************************

// ********** Begin Delegate FOnDamageReceived *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_238_DELEGATE \
static void FOnDamageReceived_DelegateWrapper(const FMulticastScriptDelegate& OnDamageReceived, float DamageAmount, AActor* DamageSource);


// ********** End Delegate FOnDamageReceived *******************************************************

// ********** Begin Delegate FOnActorDeath *********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_239_DELEGATE \
static void FOnActorDeath_DelegateWrapper(const FMulticastScriptDelegate& OnActorDeath, ARTSBaseActor* DeadActor);


// ********** End Delegate FOnActorDeath ***********************************************************

// ********** Begin Delegate FOnSelectionChanged ***************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_240_DELEGATE \
static void FOnSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSelectionChanged, ARTSBaseActor* Actor, bool bSelected);


// ********** End Delegate FOnSelectionChanged *****************************************************

// ********** Begin Class ARTSBaseActor ************************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCanProduceTechLevel); \
	DECLARE_FUNCTION(execSetTechLevel); \
	DECLARE_FUNCTION(execGetTechLevel); \
	DECLARE_FUNCTION(execGetActorTeamID); \
	DECLARE_FUNCTION(execIsEnemyActor); \
	DECLARE_FUNCTION(execIsOnSameTeamAsActor); \
	DECLARE_FUNCTION(execIsEnemy); \
	DECLARE_FUNCTION(execIsOnSameTeam); \
	DECLARE_FUNCTION(execSetTeamID); \
	DECLARE_FUNCTION(execGetTeamID); \
	DECLARE_FUNCTION(execIsBuilding); \
	DECLARE_FUNCTION(execIsUnit); \
	DECLARE_FUNCTION(execGetActorType); \
	DECLARE_FUNCTION(execCanBeSelected); \
	DECLARE_FUNCTION(execSetSelected); \
	DECLARE_FUNCTION(execIsDead); \
	DECLARE_FUNCTION(execIsAlive); \
	DECLARE_FUNCTION(execGetHealthPercentage); \
	DECLARE_FUNCTION(execGetMaxHealth); \
	DECLARE_FUNCTION(execGetCurrentHealth); \
	DECLARE_FUNCTION(execGetHealth); \
	DECLARE_FUNCTION(execSetHealth); \
	DECLARE_FUNCTION(execHeal); \
	DECLARE_FUNCTION(execTakeDamageSimple); \
	DECLARE_FUNCTION(execRemoveGameplayTags); \
	DECLARE_FUNCTION(execAddGameplayTags); \
	DECLARE_FUNCTION(execRemoveGameplayTag); \
	DECLARE_FUNCTION(execAddGameplayTag); \
	DECLARE_FUNCTION(execHasAllGameplayTags); \
	DECLARE_FUNCTION(execHasAnyGameplayTags); \
	DECLARE_FUNCTION(execHasGameplayTag);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARTSBaseActor(); \
	friend struct Z_Construct_UClass_ARTSBaseActor_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister(); \
public: \
	DECLARE_CLASS2(ARTSBaseActor, APawn, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_ARTSBaseActor_NoRegister) \
	DECLARE_SERIALIZER(ARTSBaseActor)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARTSBaseActor(ARTSBaseActor&&) = delete; \
	ARTSBaseActor(const ARTSBaseActor&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARTSBaseActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARTSBaseActor); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(ARTSBaseActor) \
	NO_API virtual ~ARTSBaseActor();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_54_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h_57_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARTSBaseActor;

// ********** End Class ARTSBaseActor **************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h

// ********** Begin Enum ERTSActorType *************************************************************
#define FOREACH_ENUM_ERTSACTORTYPE(op) \
	op(ERTSActorType::Unit) \
	op(ERTSActorType::Building) 

enum class ERTSActorType : uint8;
template<> struct TIsUEnumClass<ERTSActorType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSActorType>();
// ********** End Enum ERTSActorType ***************************************************************

// ********** Begin Enum ERTSUnitDomain ************************************************************
#define FOREACH_ENUM_ERTSUNITDOMAIN(op) \
	op(ERTSUnitDomain::None) \
	op(ERTSUnitDomain::Land) \
	op(ERTSUnitDomain::Air) \
	op(ERTSUnitDomain::Sea) \
	op(ERTSUnitDomain::Subnautical) 

enum class ERTSUnitDomain : uint8;
template<> struct TIsUEnumClass<ERTSUnitDomain> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSUnitDomain>();
// ********** End Enum ERTSUnitDomain **************************************************************

// ********** Begin Enum ERTSAircraftType **********************************************************
#define FOREACH_ENUM_ERTSAIRCRAFTTYPE(op) \
	op(ERTSAircraftType::None) \
	op(ERTSAircraftType::VTOL) \
	op(ERTSAircraftType::FixedWing) \
	op(ERTSAircraftType::Hybrid) 

enum class ERTSAircraftType : uint8;
template<> struct TIsUEnumClass<ERTSAircraftType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSAircraftType>();
// ********** End Enum ERTSAircraftType ************************************************************

// ********** Begin Enum ERTSTechLevel *************************************************************
#define FOREACH_ENUM_ERTSTECHLEVEL(op) \
	op(ERTSTechLevel::Tech1) \
	op(ERTSTechLevel::Tech2) \
	op(ERTSTechLevel::Tech3) \
	op(ERTSTechLevel::Tech4) 

enum class ERTSTechLevel : uint8;
template<> struct TIsUEnumClass<ERTSTechLevel> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTechLevel>();
// ********** End Enum ERTSTechLevel ***************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
