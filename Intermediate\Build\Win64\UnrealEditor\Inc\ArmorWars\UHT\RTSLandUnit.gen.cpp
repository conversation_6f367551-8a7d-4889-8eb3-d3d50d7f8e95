// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSLandUnit.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSLandUnit() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSLandUnit();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSLandUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit();
ARMORWARS_API UClass* Z_Construct_UClass_URTSLandMovementComponent_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSTerrainType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FTerrainMovementData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSTerrainType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSTerrainType;
static UEnum* ERTSTerrainType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSTerrainType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSTerrainType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSTerrainType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSTerrainType"));
	}
	return Z_Registration_Info_UEnum_ERTSTerrainType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTerrainType>()
{
	return ERTSTerrainType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for terrain types\n" },
#endif
		{ "Desert.DisplayName", "Desert" },
		{ "Desert.Name", "ERTSTerrainType::Desert" },
		{ "Forest.DisplayName", "Forest" },
		{ "Forest.Name", "ERTSTerrainType::Forest" },
		{ "Grass.DisplayName", "Grass" },
		{ "Grass.Name", "ERTSTerrainType::Grass" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
		{ "Mountain.DisplayName", "Mountain" },
		{ "Mountain.Name", "ERTSTerrainType::Mountain" },
		{ "Road.DisplayName", "Road" },
		{ "Road.Name", "ERTSTerrainType::Road" },
		{ "Swamp.DisplayName", "Swamp" },
		{ "Swamp.Name", "ERTSTerrainType::Swamp" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for terrain types" },
#endif
		{ "Urban.DisplayName", "Urban" },
		{ "Urban.Name", "ERTSTerrainType::Urban" },
		{ "Water.DisplayName", "Water" },
		{ "Water.Name", "ERTSTerrainType::Water" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSTerrainType::Road", (int64)ERTSTerrainType::Road },
		{ "ERTSTerrainType::Grass", (int64)ERTSTerrainType::Grass },
		{ "ERTSTerrainType::Forest", (int64)ERTSTerrainType::Forest },
		{ "ERTSTerrainType::Desert", (int64)ERTSTerrainType::Desert },
		{ "ERTSTerrainType::Mountain", (int64)ERTSTerrainType::Mountain },
		{ "ERTSTerrainType::Swamp", (int64)ERTSTerrainType::Swamp },
		{ "ERTSTerrainType::Urban", (int64)ERTSTerrainType::Urban },
		{ "ERTSTerrainType::Water", (int64)ERTSTerrainType::Water },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSTerrainType",
	"ERTSTerrainType",
	Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSTerrainType()
{
	if (!Z_Registration_Info_UEnum_ERTSTerrainType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSTerrainType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSTerrainType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSTerrainType.InnerSingleton;
}
// ********** End Enum ERTSTerrainType *************************************************************

// ********** Begin ScriptStruct FTerrainMovementData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTerrainMovementData;
class UScriptStruct* FTerrainMovementData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTerrainMovementData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTerrainMovementData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTerrainMovementData, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("TerrainMovementData"));
	}
	return Z_Registration_Info_UScriptStruct_FTerrainMovementData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTerrainMovementData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Struct for terrain movement modifiers\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct for terrain movement modifiers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeedMultiplier_MetaData[] = {
		{ "Category", "Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Speed multiplier for this terrain type (1.0 = normal speed)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Speed multiplier for this terrain type (1.0 = normal speed)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanTraverse_MetaData[] = {
		{ "Category", "Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit can traverse this terrain\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit can traverse this terrain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConsumptionMultiplier_MetaData[] = {
		{ "Category", "Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Energy consumption multiplier (fuel removed except for air units)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Energy consumption multiplier (fuel removed except for air units)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static void NewProp_bCanTraverse_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanTraverse;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConsumptionMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTerrainMovementData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTerrainMovementData, SpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeedMultiplier_MetaData), NewProp_SpeedMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_bCanTraverse_SetBit(void* Obj)
{
	((FTerrainMovementData*)Obj)->bCanTraverse = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_bCanTraverse = { "bCanTraverse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTerrainMovementData), &Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_bCanTraverse_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanTraverse_MetaData), NewProp_bCanTraverse_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_ConsumptionMultiplier = { "ConsumptionMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTerrainMovementData, ConsumptionMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConsumptionMultiplier_MetaData), NewProp_ConsumptionMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTerrainMovementData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_SpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_bCanTraverse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewProp_ConsumptionMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTerrainMovementData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTerrainMovementData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"TerrainMovementData",
	Z_Construct_UScriptStruct_FTerrainMovementData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTerrainMovementData_Statics::PropPointers),
	sizeof(FTerrainMovementData),
	alignof(FTerrainMovementData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTerrainMovementData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTerrainMovementData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTerrainMovementData()
{
	if (!Z_Registration_Info_UScriptStruct_FTerrainMovementData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTerrainMovementData.InnerSingleton, Z_Construct_UScriptStruct_FTerrainMovementData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTerrainMovementData.InnerSingleton;
}
// ********** End ScriptStruct FTerrainMovementData ************************************************

// ********** Begin Delegate FOnTerrainChanged *****************************************************
struct Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics
{
	struct RTSLandUnit_eventOnTerrainChanged_Parms
	{
		ARTSLandUnit* LandUnit;
		ERTSTerrainType NewTerrain;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LandUnit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewTerrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewTerrain;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::NewProp_LandUnit = { "LandUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventOnTerrainChanged_Parms, LandUnit), Z_Construct_UClass_ARTSLandUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::NewProp_NewTerrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::NewProp_NewTerrain = { "NewTerrain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventOnTerrainChanged_Parms, NewTerrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::NewProp_LandUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::NewProp_NewTerrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::NewProp_NewTerrain,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "OnTerrainChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::RTSLandUnit_eventOnTerrainChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::RTSLandUnit_eventOnTerrainChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSLandUnit::FOnTerrainChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTerrainChanged, ARTSLandUnit* LandUnit, ERTSTerrainType NewTerrain)
{
	struct RTSLandUnit_eventOnTerrainChanged_Parms
	{
		ARTSLandUnit* LandUnit;
		ERTSTerrainType NewTerrain;
	};
	RTSLandUnit_eventOnTerrainChanged_Parms Parms;
	Parms.LandUnit=LandUnit;
	Parms.NewTerrain=NewTerrain;
	OnTerrainChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTerrainChanged *******************************************************

// ********** Begin Class ARTSLandUnit Function CanTraverseTerrain *********************************
struct Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics
{
	struct RTSLandUnit_eventCanTraverseTerrain_Parms
	{
		ERTSTerrainType Terrain;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Movement" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Terrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Terrain;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_Terrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_Terrain = { "Terrain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventCanTraverseTerrain_Parms, Terrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
void Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSLandUnit_eventCanTraverseTerrain_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSLandUnit_eventCanTraverseTerrain_Parms), &Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_Terrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_Terrain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "CanTraverseTerrain", Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::RTSLandUnit_eventCanTraverseTerrain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::RTSLandUnit_eventCanTraverseTerrain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execCanTraverseTerrain)
{
	P_GET_ENUM(ERTSTerrainType,Z_Param_Terrain);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanTraverseTerrain(ERTSTerrainType(Z_Param_Terrain));
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function CanTraverseTerrain ***********************************

// ********** Begin Class ARTSLandUnit Function DetectTerrainType **********************************
struct Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics
{
	struct RTSLandUnit_eventDetectTerrainType_Parms
	{
		ERTSTerrainType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Terrain functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrain functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventDetectTerrainType_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "DetectTerrainType", Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::RTSLandUnit_eventDetectTerrainType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::RTSLandUnit_eventDetectTerrainType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execDetectTerrainType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSTerrainType*)Z_Param__Result=P_THIS->DetectTerrainType();
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function DetectTerrainType ************************************

// ********** Begin Class ARTSLandUnit Function GetCurrentSpeedMultiplier **************************
struct Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics
{
	struct RTSLandUnit_eventGetCurrentSpeedMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Movement" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventGetCurrentSpeedMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "GetCurrentSpeedMultiplier", Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::RTSLandUnit_eventGetCurrentSpeedMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::RTSLandUnit_eventGetCurrentSpeedMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execGetCurrentSpeedMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentSpeedMultiplier();
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function GetCurrentSpeedMultiplier ****************************

// ********** Begin Class ARTSLandUnit Function GetTerrainMovementData *****************************
struct Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics
{
	struct RTSLandUnit_eventGetTerrainMovementData_Parms
	{
		ERTSTerrainType Terrain;
		FTerrainMovementData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Terrain" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Terrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Terrain;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::NewProp_Terrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::NewProp_Terrain = { "Terrain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventGetTerrainMovementData_Parms, Terrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventGetTerrainMovementData_Parms, ReturnValue), Z_Construct_UScriptStruct_FTerrainMovementData, METADATA_PARAMS(0, nullptr) }; // 1001909579
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::NewProp_Terrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::NewProp_Terrain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "GetTerrainMovementData", Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::RTSLandUnit_eventGetTerrainMovementData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::RTSLandUnit_eventGetTerrainMovementData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execGetTerrainMovementData)
{
	P_GET_ENUM(ERTSTerrainType,Z_Param_Terrain);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FTerrainMovementData*)Z_Param__Result=P_THIS->GetTerrainMovementData(ERTSTerrainType(Z_Param_Terrain));
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function GetTerrainMovementData *******************************

// ********** Begin Class ARTSLandUnit Function OnTerrainChangedEvent ******************************
struct RTSLandUnit_eventOnTerrainChangedEvent_Parms
{
	ERTSTerrainType OldTerrain;
	ERTSTerrainType NewTerrain;
};
static FName NAME_ARTSLandUnit_OnTerrainChangedEvent = FName(TEXT("OnTerrainChangedEvent"));
void ARTSLandUnit::OnTerrainChangedEvent(ERTSTerrainType OldTerrain, ERTSTerrainType NewTerrain)
{
	RTSLandUnit_eventOnTerrainChangedEvent_Parms Parms;
	Parms.OldTerrain=OldTerrain;
	Parms.NewTerrain=NewTerrain;
	UFunction* Func = FindFunctionChecked(NAME_ARTSLandUnit_OnTerrainChangedEvent);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldTerrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldTerrain;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewTerrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewTerrain;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_OldTerrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_OldTerrain = { "OldTerrain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventOnTerrainChangedEvent_Parms, OldTerrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_NewTerrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_NewTerrain = { "NewTerrain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventOnTerrainChangedEvent_Parms, NewTerrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_OldTerrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_OldTerrain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_NewTerrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::NewProp_NewTerrain,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "OnTerrainChangedEvent", Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::PropPointers), sizeof(RTSLandUnit_eventOnTerrainChangedEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSLandUnit_eventOnTerrainChangedEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSLandUnit Function OnTerrainChangedEvent ********************************

// ********** Begin Class ARTSLandUnit Function SetDestination *************************************
struct Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics
{
	struct RTSLandUnit_eventSetDestination_Parms
	{
		FVector Destination;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Destination_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Destination;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::NewProp_Destination = { "Destination", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventSetDestination_Parms, Destination), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Destination_MetaData), NewProp_Destination_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::NewProp_Destination,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "SetDestination", Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::RTSLandUnit_eventSetDestination_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::RTSLandUnit_eventSetDestination_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_SetDestination()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_SetDestination_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execSetDestination)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Destination);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDestination(Z_Param_Out_Destination);
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function SetDestination ***************************************

// ********** Begin Class ARTSLandUnit Function SetTerrainMovementData *****************************
struct Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics
{
	struct RTSLandUnit_eventSetTerrainMovementData_Parms
	{
		ERTSTerrainType Terrain;
		FTerrainMovementData MovementData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Terrain" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Terrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Terrain;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::NewProp_Terrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::NewProp_Terrain = { "Terrain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventSetTerrainMovementData_Parms, Terrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::NewProp_MovementData = { "MovementData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandUnit_eventSetTerrainMovementData_Parms, MovementData), Z_Construct_UScriptStruct_FTerrainMovementData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementData_MetaData), NewProp_MovementData_MetaData) }; // 1001909579
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::NewProp_Terrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::NewProp_Terrain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::NewProp_MovementData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "SetTerrainMovementData", Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::RTSLandUnit_eventSetTerrainMovementData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::RTSLandUnit_eventSetTerrainMovementData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execSetTerrainMovementData)
{
	P_GET_ENUM(ERTSTerrainType,Z_Param_Terrain);
	P_GET_STRUCT_REF(FTerrainMovementData,Z_Param_Out_MovementData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTerrainMovementData(ERTSTerrainType(Z_Param_Terrain),Z_Param_Out_MovementData);
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function SetTerrainMovementData *******************************

// ********** Begin Class ARTSLandUnit Function UpdateTerrainType **********************************
struct Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Unit|Movement" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSLandUnit, nullptr, "UpdateTerrainType", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSLandUnit::execUpdateTerrainType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTerrainType();
	P_NATIVE_END;
}
// ********** End Class ARTSLandUnit Function UpdateTerrainType ************************************

// ********** Begin Class ARTSLandUnit *************************************************************
void ARTSLandUnit::StaticRegisterNativesARTSLandUnit()
{
	UClass* Class = ARTSLandUnit::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanTraverseTerrain", &ARTSLandUnit::execCanTraverseTerrain },
		{ "DetectTerrainType", &ARTSLandUnit::execDetectTerrainType },
		{ "GetCurrentSpeedMultiplier", &ARTSLandUnit::execGetCurrentSpeedMultiplier },
		{ "GetTerrainMovementData", &ARTSLandUnit::execGetTerrainMovementData },
		{ "SetDestination", &ARTSLandUnit::execSetDestination },
		{ "SetTerrainMovementData", &ARTSLandUnit::execSetTerrainMovementData },
		{ "UpdateTerrainType", &ARTSLandUnit::execUpdateTerrainType },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSLandUnit;
UClass* ARTSLandUnit::GetPrivateStaticClass()
{
	using TClass = ARTSLandUnit;
	if (!Z_Registration_Info_UClass_ARTSLandUnit.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSLandUnit"),
			Z_Registration_Info_UClass_ARTSLandUnit.InnerSingleton,
			StaticRegisterNativesARTSLandUnit,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSLandUnit.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSLandUnit_NoRegister()
{
	return ARTSLandUnit::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSLandUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Specialized land unit class with ground movement and terrain-specific features\n * Inherits from RTSUnit and adds terrain navigation, ground combat, and logistics\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "RTSLandUnit.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Specialized land unit class with ground movement and terrain-specific features\nInherits from RTSUnit and adds terrain navigation, ground combat, and logistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainMovementData_MetaData[] = {
		{ "Category", "Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Terrain movement modifiers\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrain movement modifiers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTerrain_MetaData[] = {
		{ "Category", "Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current terrain type\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current terrain type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTerrainSpeedMultiplier_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for terrain speed multiplier to avoid excessive updates\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for terrain speed multiplier to avoid excessive updates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTerrainCheckLocation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for terrain update optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for terrain update optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTerrainUpdateTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanCrossWater_MetaData[] = {
		{ "Category", "Land Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit can cross water\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit can cross water" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAmphibious_MetaData[] = {
		{ "Category", "Land Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this unit is amphibious\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this unit is amphibious" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandMovementComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n// NOTE: This movement component is WORKING and ACTIVELY USED - DO NOT REMOVE\n// It handles realistic vehicle movement with turning, deceleration, and collision avoidance\n// Coordinates with RTSUnit base class for formation movement and state tracking\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components\nNOTE: This movement component is WORKING and ACTIVELY USED - DO NOT REMOVE\nIt handles realistic vehicle movement with turning, deceleration, and collision avoidance\nCoordinates with RTSUnit base class for formation movement and state tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTerrainChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSLandUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TerrainMovementData_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TerrainMovementData_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TerrainMovementData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TerrainMovementData;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTerrain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTerrain;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastTerrainSpeedMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastTerrainCheckLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastTerrainUpdateTime;
	static void NewProp_bCanCrossWater_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanCrossWater;
	static void NewProp_bIsAmphibious_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAmphibious;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LandMovementComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTerrainChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSLandUnit_CanTraverseTerrain, "CanTraverseTerrain" }, // 2886819686
		{ &Z_Construct_UFunction_ARTSLandUnit_DetectTerrainType, "DetectTerrainType" }, // 2348211909
		{ &Z_Construct_UFunction_ARTSLandUnit_GetCurrentSpeedMultiplier, "GetCurrentSpeedMultiplier" }, // 3831198695
		{ &Z_Construct_UFunction_ARTSLandUnit_GetTerrainMovementData, "GetTerrainMovementData" }, // 2995883518
		{ &Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature, "OnTerrainChanged__DelegateSignature" }, // 1285238426
		{ &Z_Construct_UFunction_ARTSLandUnit_OnTerrainChangedEvent, "OnTerrainChangedEvent" }, // 1581712510
		{ &Z_Construct_UFunction_ARTSLandUnit_SetDestination, "SetDestination" }, // 3327903068
		{ &Z_Construct_UFunction_ARTSLandUnit_SetTerrainMovementData, "SetTerrainMovementData" }, // 2025471835
		{ &Z_Construct_UFunction_ARTSLandUnit_UpdateTerrainType, "UpdateTerrainType" }, // 2529014092
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSLandUnit>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData_ValueProp = { "TerrainMovementData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FTerrainMovementData, METADATA_PARAMS(0, nullptr) }; // 1001909579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData_Key_KeyProp = { "TerrainMovementData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(0, nullptr) }; // 3340463036
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData = { "TerrainMovementData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, TerrainMovementData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainMovementData_MetaData), NewProp_TerrainMovementData_MetaData) }; // 3340463036 1001909579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_CurrentTerrain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_CurrentTerrain = { "CurrentTerrain", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, CurrentTerrain), Z_Construct_UEnum_ArmorWars_ERTSTerrainType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTerrain_MetaData), NewProp_CurrentTerrain_MetaData) }; // 3340463036
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LastTerrainSpeedMultiplier = { "LastTerrainSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, LastTerrainSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTerrainSpeedMultiplier_MetaData), NewProp_LastTerrainSpeedMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LastTerrainCheckLocation = { "LastTerrainCheckLocation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, LastTerrainCheckLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTerrainCheckLocation_MetaData), NewProp_LastTerrainCheckLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LastTerrainUpdateTime = { "LastTerrainUpdateTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, LastTerrainUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTerrainUpdateTime_MetaData), NewProp_LastTerrainUpdateTime_MetaData) };
void Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bCanCrossWater_SetBit(void* Obj)
{
	((ARTSLandUnit*)Obj)->bCanCrossWater = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bCanCrossWater = { "bCanCrossWater", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSLandUnit), &Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bCanCrossWater_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanCrossWater_MetaData), NewProp_bCanCrossWater_MetaData) };
void Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bIsAmphibious_SetBit(void* Obj)
{
	((ARTSLandUnit*)Obj)->bIsAmphibious = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bIsAmphibious = { "bIsAmphibious", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSLandUnit), &Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bIsAmphibious_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAmphibious_MetaData), NewProp_bIsAmphibious_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LandMovementComponent = { "LandMovementComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, LandMovementComponent), Z_Construct_UClass_URTSLandMovementComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandMovementComponent_MetaData), NewProp_LandMovementComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_OnTerrainChanged = { "OnTerrainChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSLandUnit, OnTerrainChanged), Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTerrainChanged_MetaData), NewProp_OnTerrainChanged_MetaData) }; // 1285238426
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSLandUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_TerrainMovementData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_CurrentTerrain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_CurrentTerrain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LastTerrainSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LastTerrainCheckLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LastTerrainUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bCanCrossWater,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_bIsAmphibious,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_LandMovementComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSLandUnit_Statics::NewProp_OnTerrainChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSLandUnit_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSLandUnit_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ARTSUnit,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSLandUnit_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSLandUnit_Statics::ClassParams = {
	&ARTSLandUnit::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSLandUnit_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSLandUnit_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSLandUnit_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSLandUnit_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSLandUnit()
{
	if (!Z_Registration_Info_UClass_ARTSLandUnit.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSLandUnit.OuterSingleton, Z_Construct_UClass_ARTSLandUnit_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSLandUnit.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSLandUnit);
ARTSLandUnit::~ARTSLandUnit() {}
// ********** End Class ARTSLandUnit ***************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSTerrainType_StaticEnum, TEXT("ERTSTerrainType"), &Z_Registration_Info_UEnum_ERTSTerrainType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3340463036U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FTerrainMovementData::StaticStruct, Z_Construct_UScriptStruct_FTerrainMovementData_Statics::NewStructOps, TEXT("TerrainMovementData"), &Z_Registration_Info_UScriptStruct_FTerrainMovementData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTerrainMovementData), 1001909579U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSLandUnit, ARTSLandUnit::StaticClass, TEXT("ARTSLandUnit"), &Z_Registration_Info_UClass_ARTSLandUnit, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSLandUnit), 1705791617U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_3035998678(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
