#include "RTSTeamManager.h"
#include "RTSBaseActor.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"

URTSTeamManager::URTSTeamManager()
{
}

void URTSTeamManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogTemp, Log, TEXT("RTSTeamManager initialized"));
}

void URTSTeamManager::Deinitialize()
{
    ClearAllTeams();
    Super::Deinitialize();
    
    UE_LOG(LogTemp, Log, TEXT("RTSTeamManager deinitialized"));
}

bool URTSTeamManager::CreateTeam(int32 TeamID, const FText& TeamName, bool bIsAI)
{
    if (DoesTeamExist(TeamID))
    {
        UE_LOG(LogTemp, Warning, TEXT("Team %d already exists"), TeamID);
        return false;
    }
    
    FRTSTeamInfo NewTeam(TeamID, TeamName);
    NewTeam.bIsAIControlled = bIsAI;
    
    Teams.Add(TeamID, NewTeam);
    
    OnTeamCreated.Broadcast(TeamID, NewTeam);
    
    UE_LOG(LogTemp, Log, TEXT("Created team %d: %s"), TeamID, *TeamName.ToString());
    return true;
}

bool URTSTeamManager::RemoveTeam(int32 TeamID)
{
    if (!DoesTeamExist(TeamID))
    {
        return false;
    }
    
    Teams.Remove(TeamID);
    OnTeamRemoved.Broadcast(TeamID);
    
    UE_LOG(LogTemp, Log, TEXT("Removed team %d"), TeamID);
    return true;
}

bool URTSTeamManager::DoesTeamExist(int32 TeamID) const
{
    return Teams.Contains(TeamID);
}

FRTSTeamInfo URTSTeamManager::GetTeamInfo(int32 TeamID) const
{
    if (const FRTSTeamInfo* TeamInfo = Teams.Find(TeamID))
    {
        return *TeamInfo;
    }
    
    return FRTSTeamInfo();
}

bool URTSTeamManager::SetTeamInfo(int32 TeamID, const FRTSTeamInfo& NewTeamInfo)
{
    if (!DoesTeamExist(TeamID))
    {
        return false;
    }
    
    Teams[TeamID] = NewTeamInfo;
    Teams[TeamID].TeamID = TeamID; // Ensure TeamID stays consistent
    
    return true;
}

TArray<int32> URTSTeamManager::GetAllTeamIDs() const
{
    TArray<int32> TeamIDs;
    Teams.GetKeys(TeamIDs);
    return TeamIDs;
}

TArray<FRTSTeamInfo> URTSTeamManager::GetAllTeams() const
{
    TArray<FRTSTeamInfo> AllTeams;
    for (const auto& TeamPair : Teams)
    {
        AllTeams.Add(TeamPair.Value);
    }
    return AllTeams;
}

bool URTSTeamManager::AreTeamsAllied(int32 TeamA, int32 TeamB) const
{
    // Same team is allied
    return TeamA == TeamB;
}

bool URTSTeamManager::AreTeamsEnemies(int32 TeamA, int32 TeamB) const
{
    // Different teams are enemies
    return TeamA != TeamB;
}

bool URTSTeamManager::AreActorsAllied(const ARTSBaseActor* ActorA, const ARTSBaseActor* ActorB) const
{
    if (!ActorA || !ActorB)
    {
        return false;
    }
    
    return AreTeamsAllied(ActorA->GetTeamID(), ActorB->GetTeamID());
}

bool URTSTeamManager::AreActorsEnemies(const ARTSBaseActor* ActorA, const ARTSBaseActor* ActorB) const
{
    if (!ActorA || !ActorB)
    {
        return false;
    }
    
    return AreTeamsEnemies(ActorA->GetTeamID(), ActorB->GetTeamID());
}

TArray<ARTSBaseActor*> URTSTeamManager::GetAllActorsOnTeam(int32 TeamID) const
{
    TArray<ARTSBaseActor*> TeamActors;
    
    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
            {
                if (RTSActor->GetTeamID() == TeamID)
                {
                    TeamActors.Add(RTSActor);
                }
            }
        }
    }
    
    return TeamActors;
}

TArray<ARTSBaseActor*> URTSTeamManager::GetAllEnemyActors(int32 TeamID) const
{
    TArray<ARTSBaseActor*> EnemyActors;
    
    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
            {
                if (AreTeamsEnemies(TeamID, RTSActor->GetTeamID()))
                {
                    EnemyActors.Add(RTSActor);
                }
            }
        }
    }
    
    return EnemyActors;
}

void URTSTeamManager::SetTeamActive(int32 TeamID, bool bActive)
{
    if (FRTSTeamInfo* TeamInfo = Teams.Find(TeamID))
    {
        TeamInfo->bIsActive = bActive;
        UE_LOG(LogTemp, Log, TEXT("Team %d set to %s"), TeamID, bActive ? TEXT("active") : TEXT("inactive"));
    }
}

bool URTSTeamManager::IsTeamActive(int32 TeamID) const
{
    if (const FRTSTeamInfo* TeamInfo = Teams.Find(TeamID))
    {
        return TeamInfo->bIsActive;
    }
    
    return false;
}

int32 URTSTeamManager::GetActiveTeamCount() const
{
    int32 ActiveCount = 0;
    for (const auto& TeamPair : Teams)
    {
        if (TeamPair.Value.bIsActive)
        {
            ActiveCount++;
        }
    }
    return ActiveCount;
}

void URTSTeamManager::InitializeDefaultTeams()
{
    ClearAllTeams();
    
    // Create default teams
    CreateTeam(0, FText::FromString(TEXT("Player")), false);
    CreateTeam(1, FText::FromString(TEXT("AI Enemy")), true);
    CreateTeam(2, FText::FromString(TEXT("Neutral")), true);
    
    UE_LOG(LogTemp, Log, TEXT("Initialized default teams"));
}

void URTSTeamManager::ClearAllTeams()
{
    Teams.Empty();
    UE_LOG(LogTemp, Log, TEXT("Cleared all teams"));
}

// Enhanced AI support functions
TArray<ARTSBaseActor*> URTSTeamManager::FindEnemiesInRange(int32 TeamID, const FVector& Location, float Range) const
{
    TArray<ARTSBaseActor*> EnemiesInRange;

    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);

        for (AActor* Actor : FoundActors)
        {
            if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
            {
                if (AreTeamsEnemies(TeamID, RTSActor->GetTeamID()) && RTSActor->IsAlive())
                {
                    float Distance = FVector::Dist(Location, RTSActor->GetActorLocation());
                    if (Distance <= Range)
                    {
                        EnemiesInRange.Add(RTSActor);
                    }
                }
            }
        }
    }

    return EnemiesInRange;
}

ARTSBaseActor* URTSTeamManager::FindNearestEnemy(int32 TeamID, const FVector& Location, float MaxRange) const
{
    ARTSBaseActor* NearestEnemy = nullptr;
    float NearestDistance = MaxRange > 0.0f ? MaxRange : TNumericLimits<float>::Max();

    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);

        for (AActor* Actor : FoundActors)
        {
            if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
            {
                if (AreTeamsEnemies(TeamID, RTSActor->GetTeamID()) && RTSActor->IsAlive())
                {
                    float Distance = FVector::Dist(Location, RTSActor->GetActorLocation());
                    if (Distance < NearestDistance)
                    {
                        NearestDistance = Distance;
                        NearestEnemy = RTSActor;
                    }
                }
            }
        }
    }

    return NearestEnemy;
}

ARTSBaseActor* URTSTeamManager::FindHighestPriorityTarget(int32 TeamID, const FVector& Location, float MaxRange) const
{
    ARTSBaseActor* HighestPriorityTarget = nullptr;
    float HighestPriority = 0.0f;

    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);

        for (AActor* Actor : FoundActors)
        {
            if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
            {
                if (AreTeamsEnemies(TeamID, RTSActor->GetTeamID()) && RTSActor->IsAlive())
                {
                    float Distance = FVector::Dist(Location, RTSActor->GetActorLocation());
                    if (MaxRange <= 0.0f || Distance <= MaxRange)
                    {
                        float Priority = CalculateTargetPriority(nullptr, RTSActor);
                        if (Priority > HighestPriority)
                        {
                            HighestPriority = Priority;
                            HighestPriorityTarget = RTSActor;
                        }
                    }
                }
            }
        }
    }

    return HighestPriorityTarget;
}

bool URTSTeamManager::IsValidTarget(const ARTSBaseActor* Attacker, const ARTSBaseActor* Target) const
{
    if (!Attacker || !Target)
    {
        return false;
    }

    // Must be enemies
    if (!AreActorsEnemies(Attacker, Target))
    {
        return false;
    }

    // Target must be alive
    if (!Target->IsAlive())
    {
        return false;
    }

    return true;
}

float URTSTeamManager::CalculateTargetPriority(const ARTSBaseActor* Attacker, const ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }

    float Priority = 1.0f;

    // Higher priority for lower health targets (easier to kill)
    if (Target->GetMaxHealth() > 0.0f)
    {
        float HealthRatio = Target->GetCurrentHealth() / Target->GetMaxHealth();
        Priority += (1.0f - HealthRatio) * 2.0f; // Up to +2.0 for low health targets
    }

    // Higher priority for units vs buildings
    if (Target->GetActorType() == ERTSActorType::Unit)
    {
        Priority += 1.0f;
    }

    // Distance factor (closer targets are higher priority)
    if (Attacker)
    {
        float Distance = FVector::Dist(Attacker->GetActorLocation(), Target->GetActorLocation());
        Priority += FMath::Max(0.0f, (2000.0f - Distance) / 2000.0f); // Up to +1.0 for close targets
    }

    return Priority;
}
