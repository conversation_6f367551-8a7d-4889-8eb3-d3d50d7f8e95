Here's a comprehensive prompt for a new Augment agent to systematically fix all the compilation issues:

---

# RTS AI System Compilation Fix Task

## **Context**
I have an RTS AI system implementation in UE5 that has compilation errors due to Blueprint compatibility issues, inheritance problems, and type mismatches. The core architecture is solid but needs systematic fixes to compile successfully.

## **Inheritance Structure (COMPLETED)**
- ✅ ARTSBaseActor now inherits from APawn (was AActor)
- ✅ ARTSUnit now inherits from ARTSBaseActor (was APawn)
- This creates the proper hierarchy: ARTSUnit → ARTSBaseActor → APawn

## **Your Task**
Fix ALL compilation errors systematically and ensure the project builds successfully. Work through these categories in priority order:

### **CRITICAL PRIORITY (Must fix for compilation)**

#### **1. TWeakObjectPtr → TSoftObjectPtr Replacements**
Replace ALL instances of `TWeakObjectPtr` with `TSoftObjectPtr` in implementation files (.cpp):

**Files to fix:**
- `Source/ArmorWars/Private/RTSCollisionAvoidanceSystem.cpp`
- `Source/ArmorWars/Private/RTSFormationManager.cpp`
- `Source/ArmorWars/Private/RTSReturnFireSystem.cpp`
- `Source/ArmorWars/Private/RTSAITestingSystem.cpp`
- `Source/ArmorWars/Private/RTSCombatBehaviorTree.cpp`
- `Source/ArmorWars/Private/RTSBlackboardSystem.cpp`

**Pattern:**
```cpp
// Change from:
for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : SomeArray)
// To:
for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : SomeArray)

// Also change:
TWeakObjectPtr<ARTSBaseActor> → TSoftObjectPtr<ARTSBaseActor>
```

#### **2. Add Missing Health Functions to RTSUnit**
Add these to `RTSUnit.h` public section:
```cpp
UFUNCTION(BlueprintPure, Category = "RTS|Health")
float GetCurrentHealth() const;

UFUNCTION(BlueprintPure, Category = "RTS|Health")
float GetMaxHealth() const;

UFUNCTION(BlueprintPure, Category = "RTS|Health")
float GetHealthPercentage() const;
```

Add implementations to `RTSUnit.cpp`:
```cpp
float ARTSUnit::GetCurrentHealth() const
{
    return CurrentHealth; // Use existing health property or inherit from ARTSBaseActor
}

float ARTSUnit::GetMaxHealth() const
{
    return MaxHealth; // Use existing health property or inherit from ARTSBaseActor
}

float ARTSUnit::GetHealthPercentage() const
{
    return MaxHealth > 0.0f ? CurrentHealth / MaxHealth : 0.0f;
}
```

#### **3. Fix TeamID Access Issues**
Replace direct `TeamID` access with getter calls:
```cpp
// Change from:
Unit->TeamID
// To:
Unit->GetTeamID()
```

**Files to search and fix:**
- All AI system .cpp files that access TeamID directly

#### **4. Remove Super::Tick() Calls from Subsystems**
World subsystems don't have Tick() by default. Remove these lines:
```cpp
// Remove this line from subsystem Tick functions:
Super::Tick(DeltaTime);
```

**Files to fix:**
- `RTSFormationManager.cpp`
- `RTSCollisionAvoidanceSystem.cpp` 
- `RTSAITestingSystem.cpp`

### **HIGH PRIORITY (Functionality issues)**

#### **5. Fix Invalid Cast Operations**
Remove or fix casts between unrelated types:
```cpp
// In RTSWeaponController.cpp, replace invalid casts like:
Cast<ARTSUnit>(Target) // ARTSUnit and ARTSBaseActor are now related, so this should work
```

#### **6. Add Missing Function Implementations**
Ensure all declared functions have implementations. Common missing ones:
- Formation management functions in `RTSFormationManager.cpp`
- Spatial grid functions in `RTSCollisionAvoidanceSystem.cpp`
- Testing functions in `RTSAITestingSystem.cpp`

### **MEDIUM PRIORITY (Code quality)**

#### **7. Fix Include Dependencies**
Add missing includes where needed:
- Forward declarations for classes used in headers
- Proper include statements for used classes

#### **8. Fix Function Signature Mismatches**
Ensure all function declarations match their implementations exactly.

## **Build Process**
After making fixes, build using:
```
F:\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe ArmorWarsEditor Win64 Development -Project=F:\ArmorWars\ArmorWars.uproject
```

## **Iterative Approach**
1. **Fix critical issues first** (categories 1-4)
2. **Build and check for remaining errors**
3. **Fix high priority issues** (categories 5-6)
4. **Build again and fix any remaining errors**
5. **Continue until clean build**
6. **Clean intermediate files if needed**: `Remove-Item -Recurse -Force Intermediate`

## **Success Criteria**
- ✅ Project compiles without errors
- ✅ All AI system components are properly integrated
- ✅ Blueprint compatibility maintained
- ✅ Proper inheritance hierarchy working
- ✅ No warnings related to the AI system

## **Key Files to Focus On**
**Headers (should be mostly fixed):**
- `RTSUnit.h`
- `RTSCollisionAvoidanceSystem.h`
- `RTSFormationManager.h`
- `RTSReturnFireSystem.h`
- `RTSAITestingSystem.h`
- `RTSCombatBehaviorTree.h`
- `RTSBlackboardSystem.h`

**Implementation files (need most work):**
- All corresponding `.cpp` files

## **Expected Challenges**
1. **Many TWeakObjectPtr instances** - be systematic and thorough
2. **Missing health system integration** - may need to check existing health implementation
3. **Subsystem integration** - UE5 subsystems have specific requirements
4. **Blueprint compatibility** - maintain all UPROPERTY and UFUNCTION declarations

## **Final Request**
After fixing all compilation errors and achieving a successful build, provide a summary of:
1. Total number of errors fixed
2. Key changes made
3. Any remaining warnings or issues
4. Verification that the build completed successfully

Work systematically through each category, build frequently to catch new issues early, and ensure the RTS AI system compiles and is ready for testing.

---

This prompt gives the new agent a clear roadmap to systematically fix all the compilation issues while maintaining the integrity of the RTS AI system architecture.
